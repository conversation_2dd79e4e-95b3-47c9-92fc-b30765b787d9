import json
import requests
import pymysql
import traceback
from config import ENVIRONMENTS
from logger import logger

class DataSyncer:
    def __init__(self, environment):
        self.env_config = ENVIRONMENTS[environment]
        self.strapi_url = self.env_config["strapi_url"]
        self.token = self.env_config["token"]
        self.db_config = self.env_config["database"]
    
    def init_mysql_connection(self):
        """初始化MySQL连接"""
        try:
            connection = pymysql.connect(
                host=self.db_config["host"],
                port=int(self.db_config["port"]),
                user=self.db_config["username"],
                password=self.db_config["password"],
                database=self.db_config["name"],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            logger.info(f"连接到{self.db_config['name']}数据库成功")
            return connection, "数据库连接成功"
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return None, f"数据库连接失败: {str(e)}"
    
    def upload_image(self, file_path, eid=None, field=None, table="api::episode.episode"):
        """上传图片到Strapi"""
        try:
            # 这里需要导入image_compatibility模块
            from image_compatibility import process_image_to_jpeg_stream
            
            file_name, file_content = process_image_to_jpeg_stream(file_path)
            
            files = {'files': (file_name, file_content, 'image/jpeg')}
            headers = {"Authorization": f"Bearer {self.token}"}
            
            data = {}
            if eid:
                data = {"ref": table, "refId": eid, "field": field}
            
            response = requests.post(f'{self.strapi_url}/api/upload', 
                                   headers=headers, files=files, data=data)
            
            if response.ok:
                logger.info(f"图片上传成功: {file_name}")
                return response.json(), f"图片上传成功: {file_name}"
            else:
                logger.error(f"图片上传失败: {response.status_code}")
                return None, f"图片上传失败: {response.status_code}"
                
        except Exception as e:
            logger.error(f"上传图片异常: {str(e)}")
            return None, f"上传图片异常: {str(e)}"
    
    def sync_data(self, day, progress_callback=None):
        """同步数据到指定环境"""
        data_path = f"./data/{day.replace('-', '/')}/data.json"
        
        try:
            with open(data_path, encoding="utf-8") as f:
                day_data = json.load(f)
        except FileNotFoundError:
            logger.error(f"数据文件不存在: {data_path}")
            return f"数据文件不存在: {data_path}"
        
        total = len(day_data["content"])
        processed = 0
        
        for item in day_data["content"]:
            playlet_id = item["playletId"]
            logger.info(f"开始处理 playletId: {playlet_id}")
            
            # 处理逻辑...
            processed += 1
            
            if progress_callback:
                progress_callback(processed, total, f"处理 {playlet_id}")
        
        return f"同步完成，共处理 {processed} 个项目"