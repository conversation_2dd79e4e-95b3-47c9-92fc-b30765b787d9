
import os
from datetime import datetime, timedelta

# 基础配置
DATABASE_NAME = "./data/hotdrama.db"
TABLE_NAME = "entity_data"
LOG_FILE = "./logs/app.log"
LOG_MAX_LINES = 1000

# 网络配置
os.environ["NO_PROXY"] = "localhost,127.0.0.1,***********/24"

# 环境配置
ENVIRONMENTS = {
    "test": {
        "strapi_url": "https://test-admin.hotdrama.news",
        "token": "test_token_here",
        "database": {
            "host": "test.host.com",
            "port": "3306",
            "name": "test_db",
            "username": "test_user",
            "password": "test_password"
        }
    },
    "online": {
        "strapi_url": "https://admin.hotdrama.news",
        "token": "c378b091baa7b1e9a1864e160718209716113b1229c3ea9fd0c617548ae780a8d3588495f8f935b8bb7e618ec2aa5933550fdbd83a0daf47439733838d3551b9bc0fe06cc94ad60bdc6abedd7e5993e77b6c84dd5604bc4d29e10defef029df7bfad90fac414365afcbf036905e55e3071736a28d7e04c685190ffa03ef0dafc",
        "database": {
            "host": "**************",
            "port": "3308",
            "name": "hot_drama_cms_db",
            "username": "cms_user",
            "password": "<EMAIL>"
        }
    }
}

# API配置
API_URLS = {
    "hot_ranking": "https://playlet-applet.dataeye.com/playlet/listHotRanking",
    "playlet_auth": "https://playlet-applet.dataeye.com/profile/auth?dataId={playletId}&type=1",
    "playlet_info": "https://playlet-applet.dataeye.com/playlet/getPlayletInfo?playletId={playletId}",
    "making_team": "https://playlet-applet.dataeye.com/playlet/makingTeam?playletId={playletId}",
    "hot_materials": "https://playlet-applet.dataeye.com/material/listHotMaterialRanking"
}

# AI配置
GEMINI_API_KEYS = {
    "test": "AIzaSyCZf2cxrvhOewsDRjXQ8i_h3gXluIIXIrc",
    "online": "AIzaSyD5l9EcvN9BbDlzf5rxuJzQw1YaQ3UqD6c"
}

# 视频处理配置
VIDEO_EXTRACT_DURATION = 300  # 提取前5分钟
SUMMARY_MAX_LENGTH = 300  # 摘要最大字数

# 默认抓包信息
DEFAULT_HEADERS = """Host: playlet-applet.dataeye.com
Connection: keep-alive
loginUserId: 354155
xweb_xhr: 1
S: 80cb7cdd4d4b940d7d19db19caa70013
authentication: eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDQwMDMyOTcsInVzZXJJZCI6MzU0MTU1fQ.IlllEicCYdl53Dnd54qzFwlVnCsl75q8w0HFnBMtUeo
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c33)XWEB/11581
Content-Type: application/json
Accept: */*
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://servicewechat.com/wxa4d39034d8eeffe7/106/page-frame.html
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9"""

def get_yesterday():
    """返回昨天的日期"""
    return (datetime.now() - timedelta(1)).strftime("%Y-%m-%d")


