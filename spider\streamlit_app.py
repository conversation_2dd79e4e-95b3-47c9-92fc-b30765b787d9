import streamlit as st
import requests
import os
import time
import json
import traceback
from datetime import datetime, timedelta
from urllib.parse import urlparse
import logging
import sqlite3

# Assuming these modules are in the same directory or accessible via PYTHONPATH
# These functions are expected to call the global 'append_to_output' for logging.
from sync_test_data import sync_test_data
from sync_online_data import sync_online_data
# from sync_local_data import sync_local_data # Was commented out in Gradio
from summary_test_video import summary_test_video
from summary_online_video import summary_online_video

os.environ["NO_PROXY"] = "localhost,127.0.0.1,***********/24"

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')

DATABASE_NAME = "./data/hotdrama.db"
# Note: TABLE_NAME was 'entity_data', used by insert_entity_data and get_entity_path.
# create_database also creates 'drama_summary'.
ENTITY_TABLE_NAME = "entity_data"


# --- Streamlit-specific state initialization and helper for logging ---
def initialize_session_state():
    """Initializes session state variables."""
    if 'log_lines' not in st.session_state:
        st.session_state.log_lines = []
    if 'sync_text' not in st.session_state:
        st.session_state.sync_text = ""
    # For file preview
    if 'preview_text_content' not in st.session_state:
        st.session_state.preview_text_content = ""
    if 'preview_image_path' not in st.session_state:
        st.session_state.preview_image_path = None
    if 'preview_video_path' not in st.session_state:
        st.session_state.preview_video_path = None
    if 'selected_file_explorer' not in st.session_state:
        st.session_state.selected_file_explorer = None

# This is the globally accessible append_to_output, adapted for Streamlit
def append_to_output(new_text):
    """Appends new text with a timestamp to the Streamlit log in session state."""
    if 'log_lines' not in st.session_state: # Ensure initialized
        st.session_state.log_lines = []

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    st.session_state.log_lines.append(f"[{timestamp}] {str(new_text)}") # Add timestamp
    logging.info(str(new_text)) # Add console logging
    if len(st.session_state.log_lines) > 50: # Keep last 50 lines
        st.session_state.log_lines = st.session_state.log_lines[-50:]
    st.session_state.sync_text = "\n".join(st.session_state.log_lines)
    st.rerun()

# --- Database Functions (copied from original main.py) ---
def create_database():
    """Creates the database and tables if they don't exist."""
    conn = sqlite3.connect(DATABASE_NAME)
    cursor = conn.cursor()
    cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {ENTITY_TABLE_NAME} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            entity_name TEXT UNIQUE,
            entity_path TEXT
        )
    """)
    conn.commit()

    cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS drama_summary (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            playlet_id TEXT,
            material_id TEXT UNIQUE,
            ad_title TEXT,
            video_url TEXT,
            summary TEXT
        )
    """)
    conn.commit()
    conn.close()

def insert_entity_data(entity_name, entity_path):
    """Inserts entity data into the database."""
    conn = sqlite3.connect(DATABASE_NAME)
    cursor = conn.cursor()
    try:
        cursor.execute(f"INSERT INTO {ENTITY_TABLE_NAME} (entity_name, entity_path) VALUES (?, ?)", (entity_name, entity_path))
        conn.commit()
        return f"Add Entity {entity_name} inserted with path {entity_path}."
    except sqlite3.IntegrityError:
         return f"Entity {entity_name} with path {entity_path} already exists."
    finally:
        conn.close()

def get_entity_path(entity_name):
    """Retrieves the entity path from the database."""
    conn = sqlite3.connect(DATABASE_NAME)
    cursor = conn.cursor()
    cursor.execute(f"SELECT entity_path FROM {ENTITY_TABLE_NAME} WHERE entity_name = ?", (entity_name,))
    result = cursor.fetchone()
    conn.close()
    if result:
        return result[0]
    return None

# --- Helper Functions (copied or adapted from original main.py) ---
def read_headers_from_text(text: str):
    """Reads headers from a text and returns them as a dictionary."""
    headers = {}
    for line in text.split("\n"):
        line = line.strip()
        if line and ':' in line:
            key, value = line.split(':', 1)
            headers[key.strip()] = value.strip()
    return headers

def fetch_api_data(url, headers, params=None):
    """Fetches data from an API endpoint."""
    msg = ""
    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        msg = f"Fetched data from {url} successfully."
        return response.json(), msg
    except Exception as e:
        msg = f"An unexpected error occurred: {e}"
        return None, msg

def download_image(image_url, item_id, save_dir):
    """Downloads an image/video and saves it, logging to Streamlit output."""
    msg = ""
    try:
        parsed_url = urlparse(image_url)
        filename = os.path.basename(parsed_url.path)
        if not filename: # Handle cases where path might be empty or just /
            # Create a generic filename if basename is empty, trying to get an extension
            ext = os.path.splitext(parsed_url.path)[1] or ".jpg" # Default to .jpg if no ext
            filename = f"{item_id}_image{ext}"
        else:
            filename = f"{item_id}_{filename}"

        entity_name = f"{item_id}_{filename}" # Used for DB check
        existing_path = get_entity_path(entity_name)
        if existing_path:
            msg = f"File already exists in database: {entity_name} at {existing_path}"
            return existing_path, msg

        filepath = os.path.join(save_dir, filename)
        os.makedirs(os.path.dirname(filepath), exist_ok=True) # Ensure directory exists

        response = requests.get(image_url, stream=True)
        response.raise_for_status()

        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        # Insert into DB after successful download
        db_msg = insert_entity_data(entity_name, filepath)
        msg = f"Successfully downloaded {filename} to {filepath}. {db_msg}"
        return filepath, msg
    except Exception as e:
        error_msg = f"Error downloading from {image_url}: {traceback.format_exc()}"
        # Do not call append_to_output here, return the message to the caller
        return None, error_msg


def crawl_page_generator(day, intersection_text):
    """Crawls the DataEye page data, logging to Streamlit output."""
    yield append_to_output("Starting crawl_page...")
    headers = read_headers_from_text(intersection_text)

    if headers and len(headers) > 0:
        yield append_to_output("Using headers: " + json.dumps(headers, indent=4))
    else:
        yield append_to_output("Error: Invalid headers.")
        return

    output_dir = f"./data/{day.replace('-', '/')}"
    os.makedirs(output_dir, exist_ok=True)
    requests_data = [
        "https://playlet-applet.dataeye.com/profile/auth?dataId={playletId}&type=1",
        "https://playlet-applet.dataeye.com/playlet/getPlayletInfo?playletId={playletId}",
        "https://playlet-applet.dataeye.com/playlet/makingTeam?playletId={playletId}",
        "https://playlet-applet.dataeye.com/material/selectAnchorVideos?playletId={playletId}&pageId=1&pageSize=3",
    ]

    data_file_path = os.path.join(output_dir, "data.json")
    try:
        with open(data_file_path, "r", encoding="utf-8") as f:
            dayRank = json.load(f)
    except FileNotFoundError:
        yield append_to_output(f"Error: data.json not found for {day}.")
        return
    except json.JSONDecodeError:
        yield append_to_output(f"Error: Invalid JSON format in data.json for {day}.")
        return
    
    for playletItem in dayRank.get("content", []):
        playletId = playletItem.get("playletId")
        if not playletId:
            yield append_to_output(f"Skipping item due to missing playletId: {playletItem}")
            continue
        yield append_to_output(f"Processing playletId: {playletId}")
        filename = f"{playletId}.json"
        filepath = os.path.join(output_dir, filename)

        if os.path.exists(filepath):
            yield append_to_output(f"File already exists: {filepath}")
            continue 

        resp = {}
        for request_url_template in requests_data:
            url = request_url_template.replace("{playletId}", str(playletId))
            yield append_to_output(f"Making request to: {url}")
            response_data, msg  = fetch_api_data(url, headers)
            yield append_to_output(msg)

            if response_data and "content" in response_data:
                content = response_data["content"]
                parsed_url = urlparse(url)
                path_parts = parsed_url.path.split("/")
                resp_key = path_parts[-1] if path_parts[-1] else path_parts[-2]

                if resp_key == "auth": # Skip auth response content
                    continue

                if isinstance(content, dict) or (resp_key == "makingTeam" and isinstance(content, list)): # makingTeam returns a list
                    image_save_dir = os.path.join(output_dir, str(playletId), "images")
                    video_save_dir = os.path.join(output_dir, str(playletId), "videos")
                    os.makedirs(image_save_dir, exist_ok=True)
                    os.makedirs(video_save_dir, exist_ok=True)
                    
                    try:
                        if resp_key == "getPlayletInfo" and isinstance(content, dict):
                            cover = content.get("coverOss")
                            if cover:
                                file_location, dl_msg = download_image(cover, playletId, image_save_dir)
                                yield append_to_output(dl_msg)
                                if file_location:
                                    content["coverOss"] = file_location # Update with local path
                            
                            materialList = content.get("materialList", [])
                            for material_idx, material in enumerate(materialList):
                                if isinstance(material, dict):
                                    material_id_suffix = material.get("materialId", f"m_idx{material_idx}")
                                    if material.get("cover"):
                                        file_location, dl_msg = download_image(material["cover"], f"{playletId}_m{material_id_suffix}", image_save_dir)
                                        yield append_to_output(dl_msg)
                                        if file_location:
                                            material["cover"] = file_location
                                    if material.get("videoUrl"):
                                        file_location, dl_msg = download_image(material["videoUrl"], f"{playletId}_mv{material_id_suffix}", video_save_dir)
                                        yield append_to_output(dl_msg)
                                        if file_location:
                                            material["videoUrl"] = file_location
                        
                        if resp_key == "makingTeam" and isinstance(content, list):
                            for item_idx, item in enumerate(content):
                                if isinstance(item, dict) and item.get("headPic"):
                                    item_id_suffix = item.get("id", f"team_hp_idx{item_idx}") # Use a unique part of item if available
                                    file_location, dl_msg = download_image(item["headPic"], f"{playletId}_hp{item_id_suffix}", image_save_dir)
                                    yield append_to_output(dl_msg)
                                    if file_location:
                                        item["headPic"] = file_location
                        
                        # selectAnchorVideos logic was commented out, kept for reference if needed
                        # if resp_key == "selectAnchorVideos" and isinstance(content, list):
                        #     for item_idx, item in enumerate(content):
                        #         if isinstance(item, dict): # Ensure item is a dict
                        #             item_id_suffix = item.get("id", f"anchor_v_idx{item_idx}")
                        #             if item.get("picUrl"):
                        #                 file_location, dl_msg = download_image(item["picUrl"], f"{playletId}_anchor_pic{item_id_suffix}", image_save_dir)
                        #                 yield append_to_output(dl_msg)
                        #                 if file_location: item["picUrl"] = file_location
                        #             if item.get("videoUrl"):
                        #                 file_location, dl_msg = download_image(item["videoUrl"], f"{playletId}_anchor_vid{item_id_suffix}", video_save_dir)
                        #                 yield append_to_output(dl_msg)
                        #                 if file_location: item["videoUrl"] = file_location

                        resp[resp_key] = content
                        # yield append_to_output(f"Response for URL: {url}, content processed.") # Less verbose
                    except Exception as e:
                        yield append_to_output(f"Error processing content for URL: {url}, error: {e}")
                        yield append_to_output(f"Traceback: {traceback.format_exc()}")
                else:
                    yield append_to_output(f"Error: 'content' for {url} is not a dictionary or expected list. Type: {type(content)}")
            else:
                yield append_to_output(f"No 'content' found or error in response for URL: {url}")
            
            time.sleep(1) # Respect API rate limits

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(resp, f, ensure_ascii=False, indent=4)
        yield append_to_output(f"Content saved to: {filepath}")
    yield append_to_output("crawl_page completed.")


def crawl_hot_generator(day, intersection_text):
    """Crawls DataEye hot ranking, logs to Streamlit output."""
    yield append_to_output("Starting crawl_hot...")
    headers = read_headers_from_text(intersection_text)
    if not headers:
        yield append_to_output("Error: Invalid headers.")
        return
    yield append_to_output("Using headers: " + json.dumps(headers, indent=4))

    params = {"pageId": "1", "pageSize": "10", "day": day}
    api_url = "https://playlet-applet.dataeye.com/playlet/listHotRanking"
    
    # Save covers to a general daily folder, not specific to this crawl type
    # This matches the original structure where cover_oss paths in data.json are like ./data/YYYY/MM/DD/covers/
    image_save_dir = "./data/" + day.replace("-", "/") + "/covers"
    os.makedirs(image_save_dir, exist_ok=True)

    yield append_to_output(f"Fetching data with params: {params}")
    page_data, msg = fetch_api_data(api_url, headers, params)
    yield append_to_output(msg)

    if page_data and "content" in page_data:
        for item in page_data.get("content", []):
            if isinstance(item, dict):
                playlet_name = item.get('playletName', 'Unknown Playlet')
                yield append_to_output(f"Processing item: {playlet_name} start.")
                playlet_id = item.get("playletId", "")
                image_url = item.get("coverOss", "") # Original URL
                
                if image_url and playlet_id:
                    local_image_path, dl_msg = download_image(image_url, str(playlet_id), image_save_dir)
                    yield append_to_output(dl_msg)
                    if local_image_path:
                        item["cover_oss_local"] = local_image_path # Add new key for local path
                yield append_to_output(f"Processed item: {playlet_name} finish.")
        
        # Save the main data.json in the daily folder, not under 'covers'
        data_save_dir = f"./data/{day.replace('-', '/')}"
        os.makedirs(data_save_dir, exist_ok=True)
        data_json_path = os.path.join(data_save_dir, "data.json")
        with open(data_json_path, "w", encoding="utf-8") as f:
            json.dump(page_data, f, ensure_ascii=False, indent=4)
        yield append_to_output(f"Data saved to: {data_json_path}")
    else:
        yield append_to_output("No 'content' in page_data or page_data is None.")
    yield append_to_output("crawl_hot completed.")

def read_existing_data(data_file_path):
    """Reads existing data from a JSON file."""
    if os.path.exists(data_file_path):
        with open(data_file_path, "r", encoding="utf-8") as f:
            try:
                return json.load(f)
            except json.JSONDecodeError:
                return {"content": []} # Return a default structure on error
    return {"content": []} # Return a default structure if file doesn't exist

def write_existing_data(data_file_path, data):
    """Writes data to a JSON file."""
    with open(data_file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=4)

# crawl_materials was commented out in Gradio, including it here similarly
# def crawl_materials(day, intersection_text):
#     """Crawls DataEye hot materials, logs to Streamlit output."""
#     append_to_output("Starting crawl_materials...")
#     # ... (Implementation would be similar to crawl_hot/crawl_page) ...
#     append_to_output("crawl_materials completed.")


def clear_streamlit_log():
    """Clears the Streamlit log and preview areas."""
    st.session_state.log_lines = []
    st.session_state.sync_text = ""
    st.session_state.preview_text_content = ""
    st.session_state.preview_image_path = None
    st.session_state.preview_video_path = None
    st.rerun()


def list_files_for_explorer(root_dir="./data"):
    """Lists all files in a directory recursively for the file explorer."""
    all_files = []
    if not os.path.isdir(root_dir):
        return ["Root directory not found."]
    for root, _, files in os.walk(root_dir):
        for file in files:
            all_files.append(os.path.join(root, file))
    return sorted(all_files) if all_files else ["No files found in ./data"]


# --- Wrappers for imported generator functions ---
def run_generator_task(task_function, *args):
    """ Helper to run generator tasks. It iterates through the generator,
        and calls st.rerun() after each yield to update the UI,
        allowing logs (updated via append_to_output within the task) to appear progressively.
    """
    try:
        # The task_function is a generator. Each yield should correspond to a log update.
        for _log_message_or_status in task_function(*args):
            # The yielded value itself might not be used directly if append_to_output handles state.
            # The key is that append_to_output has been called before the yield by the task_function.
            st.rerun()
    except Exception as e:
        # Ensure the error message itself is logged and displayed
        append_to_output(f"Error during {task_function.__name__}: {e}")
        append_to_output(traceback.format_exc())
        st.rerun() # Rerun to show the error in the log

# --- Streamlit UI and Main Logic ---
def main_streamlit():
    st.set_page_config(layout="wide", page_title="DataEye Crawl UI")
    initialize_session_state() # Ensure session state is ready
    create_database() # Create DB and tables if they don't exist

    # --- Monkey-patch imported modules to use Streamlit's append_to_output ---
    # This needs to be done early, after Streamlit's append_to_output is defined
    # and modules are imported at the top of the file.
    global streamlit_append_to_output # Make it accessible if not already
    streamlit_append_to_output = append_to_output

    # Assuming these modules are already imported at the top
    # import sync_test_data
    # import sync_online_data
    # import summary_test_video
    # import summary_online_video
    sync_test_data.append_to_output = streamlit_append_to_output
    sync_online_data.append_to_output = streamlit_append_to_output
    summary_test_video.append_to_output = streamlit_append_to_output
    summary_online_video.append_to_output = streamlit_append_to_output
    st.title("DataEye Crawl UI")

    # Default values
    yestoday = datetime.now() - timedelta(1)
    default_date = yestoday.strftime("%Y-%m-%d")
    default_intersection_text = """Host: playlet-applet.dataeye.com
Connection: keep-alive
loginUserId: 354155
xweb_xhr: 1
S: 80cb7cdd4d4b940d7d19db19caa70013
authentication: eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDQwMDMyOTcsInVzZXJJZCI6MzU0MTU1fQ.IlllEicCYdl53Dnd54qzFwlVnCsl75q8w0HFnBMtUeo
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c33)XWEB/11581
Content-Type: application/json
Accept: */*
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://servicewechat.com/wxa4d39034d8eeffe7/106/page-frame.html
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9"""

    col1, col2 = st.columns([1, 3])
    with col1:
        day_input = st.text_input("日期 (YYYY-MM-DD)", value=default_date)
    with col2:
        intersection_text_area = st.text_area("抓包信息", value=default_intersection_text, height=250)

    st.markdown("---")
    
    action_cols = st.columns(4)
    with action_cols[0]:
        if st.button("剧查查热力榜", key="crawl_hot_btn"):
            with st.spinner("Crawling hot data..."):
                run_generator_task(crawl_hot_generator, day_input, intersection_text_area)
        if st.button("榜单详情", key="crawl_page_btn"):
            with st.spinner("Crawling page details..."):
                run_generator_task(crawl_page_generator, day_input, intersection_text_area)
    
    with action_cols[1]:
        if st.button("同步到测试环境", key="sync_test_btn"):
            with st.spinner("同步到测试环境..."):
                run_generator_task(sync_test_data, day_input)
        if st.button("解析测试环境物料", key="summary_test_btn"):
            with st.spinner("解析测试环境物料..."):
                run_generator_task(summary_test_video, day_input)

    with action_cols[2]:
        if st.button("同步到线上环境", key="sync_online_btn"):
            with st.spinner("同步到线上环境..."):
                run_generator_task(sync_online_data, day_input)
        if st.button("解析线上环境物料", key="summary_online_btn"):
            with st.spinner("解析线上环境物料..."):
                run_generator_task(summary_online_video, day_input)
    
    with action_cols[3]:
        # sync_local_button was commented out
        # if st.button("保存本地json", key="sync_local_btn"):
        #     with st.spinner("Saving local JSON..."):
        #         run_generator_task(sync_local_data, day_input) # Assuming sync_local_data is similar
        
        if st.button("清空日志", key="clear_log_btn"):
            clear_streamlit_log()

        # crawl_materials_button was commented out
        # if st.button("剧查查物料榜", key="crawl_materials_btn"):
        #     with st.spinner("Crawling materials..."):
        #         crawl_materials(day_input, intersection_text_area)


    st.markdown("---")
    
    display_cols = st.columns(3)
    with display_cols[0]: # File Explorer
        st.subheader("文件浏览器")
        files_list = list_files_for_explorer("./data")
        st.session_state.selected_file_explorer = st.selectbox(
            "选择文件预览", 
            options=files_list, 
            index=0 if files_list and files_list[0] != "No files found in ./data" else 0, # Avoid error on empty
            key="file_explorer_selectbox"
        )
        
        preview_button_clicked = st.button("预览选中文件", key="preview_btn")

    with display_cols[1]: # Preview Area
        st.subheader("文件预览")
        if preview_button_clicked and st.session_state.selected_file_explorer and \
           st.session_state.selected_file_explorer not in ["No files found in ./data", "Root directory not found."]:
            
            file_path = st.session_state.selected_file_explorer
            # Clear previous previews before setting new ones
            st.session_state.preview_text_content = ""
            st.session_state.preview_image_path = None
            st.session_state.preview_video_path = None

            if os.path.exists(file_path):
                file_extension = os.path.splitext(file_path)[1].lower()
                if file_extension in ['.txt', '.json', '.md', '.log']:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            st.session_state.preview_text_content = f.read()
                    except Exception as e:
                        st.session_state.preview_text_content = f"Error reading file: {e}"
                elif file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                    st.session_state.preview_image_path = file_path
                elif file_extension in ['.mp4', '.avi', '.mov', '.mkv']:
                    st.session_state.preview_video_path = file_path
                else:
                    st.session_state.preview_text_content = "Unsupported file type for preview."
            else:
                st.session_state.preview_text_content = "File not found."
        
        # Display logic based on session state
        if st.session_state.get("preview_text_content"):
            st.text_area("文本预览", value=st.session_state.preview_text_content, height=400)
        if st.session_state.get("preview_image_path"):
            st.image(st.session_state.preview_image_path)
        if st.session_state.get("preview_video_path"):
            st.video(st.session_state.preview_video_path)

    with display_cols[2]: # Log Output
        st.subheader("日志")
        st.text_area("Log Output", value=st.session_state.get("sync_text", ""), height=460, key="log_output_area", disabled=True)


if __name__ == "__main__":
    main_streamlit()
