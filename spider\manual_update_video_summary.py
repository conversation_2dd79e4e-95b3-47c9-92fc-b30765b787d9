import json
import requests
import traceback
from google import genai
import time 
import sqlite3
from moviepy import VideoFileClip
import io
import os
import tempfile
import logging

from summary_online_video import *

def process_one_day_file(json_file_path):
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
        content = data.get("content", [])
        for playlet_info in content:
            if not isinstance(playlet_info, dict):
                logging.info(f"跳过非字典类型的内容: {playlet_info}")
                continue
            playlet_id = playlet_info.get("playletId")
            detail_file_path = os.path.join(dirpath, f"{playlet_id}.json")
            if not os.path.exists(detail_file_path):
                logging.info(f"未找到详细信息文件: {detail_file_path}，跳过此播放片段。")
                continue
            episodes, msg = find_episodes({"playlet_id":playlet_id})
            logging.info(msg)
            if not(episodes and "data" in episodes and len(episodes["data"]) > 0):
                logging.info(f"未找到播放片段 {playlet_id} 的短剧信息，跳过此播放片段。")
                continue

            with open(detail_file_path, 'r', encoding='utf-8') as detail_file:
                detail_json = json.load(detail_file)
            if not isinstance(detail_json, dict):
                logging.info(f"跳过非字典类型的详细信息: {detail_json}")
                continue

            ai_desc = episodes["data"][0]["ai_desc"]
            documentId = episodes["data"][0]["documentId"]
            regenerate = False
            try:
                ai_desc_data = json.loads(ai_desc)
                if type(ai_desc_data) is list:
                    for ai  in ai_desc_data:
                        if len(ai) == 0 or  ai == "null" or ai == None or ai == "":
                            regenerate = True
                            break
            except Exception as e:
                regenerate = True
            except TypeError as e:
                regenerate = True

            if regenerate:
                logging.info(f"播放片段 {playlet_id} 的 ai_desc 为空或无效，重新生成...")
                materialList = detail_json.get("getPlayletInfo", {}).get("materialList", [])
                logging.info(f"materialList: {materialList}")
                summarys = []
                for anchor_video in materialList:
                    videoUrl = anchor_video.get("videoUrl", "")
                    if videoUrl:
                        try:
                            materialId = anchor_video.get("materialId", "")
                            adTitle = anchor_video.get("title", "")
                            summaryData = get_entity_data(materialId)
                            if summaryData and len(summaryData) > 0:
                                summary = summaryData
                            elif videoUrl.startswith("http://") or videoUrl.startswith("https://"):
                                logging.info(f"视频 {videoUrl}未正常下载,跳过生成摘要")
                                continue
                            else:
                                summary, msg = realize_video(videoUrl)
                                logging.info(msg)
                                msg = insert_entity_data(playlet_id, materialId, adTitle, videoUrl, summary)
                            summarys.append({"playletId":playlet_id, "videlUrl":videoUrl, "materialId": materialId, "adTitle": adTitle, "summary": summary})
                        except Exception as e:
                            logging.error(f"处理视频 {videoUrl} 时发生错误: {traceback.format_exc()}")
                            continue
                if len(summarys) > 0:
                    with open(detail_file_path.replace(".json", "_summary.json"), "w", encoding="utf-8") as f:
                        json.dump(summarys, f, ensure_ascii=False, indent=4)

                    data = {
                        "playlet_id": str(playlet_id),
                        "ai_desc": [s["summary"] for s in summarys]
                    }
                    documentId = episodes["data"][0]["documentId"]
                    episodes, msg = update_episodes(documentId, data)
                    logging.info(msg)

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # 遍历已下载的目录日期,提取每个json文件的playlet_id，判断是否有ai_desc，不存在的则重新生成ai_desc

    for dirpath, dirnames, filenames in os.walk("./data"):
        # 检查当前目录是否包含 'data.json' 文件
        if "data.json" in filenames:
            json_file_path = os.path.join(dirpath, "data.json")
            logging.info(f"\n--- 发现文件: {json_file_path} ---")
            try:
                process_one_day_file(json_file_path)
            except Exception as e:
                logging.info(f"处理文件 {json_file_path} 时发生错误: {e}")
                traceback.print_exc()

    logging.info(f"\n所有子目录遍历完成。")
