import gradio as gr
from datetime import datetime 
import time
import logging

sync_text = ""

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')

def append_to_output(new_text):
    """Appends new text to the existing output text."""
    global sync_text
    sync_text = sync_text + new_text + "\n"
    logging.info(new_text) # Add console logging
    sync_arr = sync_text.split("\n")
    sync_text = "\n".join(sync_arr[-50:])  
    return sync_text

def gen_log():
    """Generates a log message and updates the output textbox."""

    while True:
        datetime_now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        new_log = f"This is a new log entry. {datetime_now}"
        yield append_to_output(new_log)
        time.sleep(2)

def main():
    """Creates and launches the Gradio interface."""

    with gr.<PERSON>s() as demo:
        gr.Markdown("# Test ")

        with gr.Row():

            with gr.<PERSON>umn(scale=1):
                log_button = gr.Button("gen log")

            with gr.Column():
                output_textbox = gr.Textbox(label="logs", lines=20, )
                                              
        log_button.click(gen_log, inputs=[], outputs=[output_textbox], queue=True)
      
    demo.launch(server_name="0.0.0.0", server_port=2341)

if __name__ == "__main__":
    main()
