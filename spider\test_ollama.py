import cv2
import base64
from io import BytesIO
from PIL import Image
from langchain_ollama import OllamaLLM
import os

def convert_pil_to_base64(pil_image: Image.Image) -> str:
    """
    Convert PIL image to Base64 encoded string.

    :param pil_image: PIL image
    :return: Base64 encoded string
    """
    buffered = BytesIO()

    # pil_image.save(buffered, format="JPEG")
    
    pil_image.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")
    return img_str

def extract_frames_from_video(video_path: str, num_frames: int = 8) -> list[str]:
    """
    Extracts a specified number of frames from a video file, converts them to
    base64 encoded strings.

    :param video_path: Path to the video file.
    :param num_frames: Number of frames to extract.
    :return: A list of base64 encoded frame strings.
    """
    if not os.path.exists(video_path):
        print(f"Error: Video file not found at {video_path}")
        return []

    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"Error: Could not open video file {video_path}")
        return []

    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if total_frames == 0:
        print(f"Error: Video {video_path} has no frames or could not read metadata.")
        cap.release()
        return []

    frame_indices_to_extract = []
    if num_frames == 1:
        frame_indices_to_extract = [total_frames // 2]  # Middle frame
    elif num_frames >= total_frames:
        frame_indices_to_extract = list(range(total_frames)) # Take all frames
    else: # num_frames < total_frames and num_frames > 1
        for i in range(num_frames):
            # Distribute frames across the video
            idx = int(i * total_frames / num_frames)
            frame_indices_to_extract.append(min(idx, total_frames - 1)) # Cap at last frame index
        frame_indices_to_extract = sorted(list(set(frame_indices_to_extract))) # Ensure unique, sorted indices

    base64_encoded_frames = []
    extracted_count = 0
    for frame_idx in frame_indices_to_extract:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if ret:
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)
            
            # Resize image to reduce size (good for LLM context and speed)
            # Max 512px on the longest side, maintains aspect ratio
            pil_image.thumbnail((512, 512)) 
            
            base64_encoded_frames.append(convert_pil_to_base64(pil_image))
            extracted_count += 1
        else:
            print(f"Warning: Could not read frame at index {frame_idx} from {video_path}")

    cap.release()

    if extracted_count == 0 and total_frames > 0:
        print(f"Error: Failed to extract any frames from {video_path} which has {total_frames} frames.")
    elif extracted_count < len(frame_indices_to_extract):
         print(f"Warning: Extracted {extracted_count} frames, less than planned {len(frame_indices_to_extract)} from {video_path}")

    return base64_encoded_frames

def test_video_plot_summary(video_file_path: str):
    """
    Takes a video file, extracts frames, and uses qwen2.5vl:latest
    via OllamaLLM to get a plot summary, then prints the summary.
    """
    print(f"Processing video: {video_file_path}")
    
    base64_frames = extract_frames_from_video(video_file_path, num_frames=8)

    if not base64_frames:
        print("No frames extracted from the video. Cannot proceed with LLM.")
        return

    print(f"Successfully extracted {len(base64_frames)} frames for the LLM.")

    try:
        llm = OllamaLLM(model="qwen2.5vl:latest")
    except Exception as e:
        print(f"Error initializing OllamaLLM: {e}")
        print("Please ensure Ollama is running and the model 'qwen2.5vl:latest' is pulled (`ollama pull qwen2.5vl:latest`).")
        return

    llm_with_video_context = llm.bind(images=base64_frames)

    prompt = "这是一段视频的连续关键帧。解析这些帧，整理为一篇叙事的小说片段，保留角色，场景信息，突出故事的剧情冲突和吸引读者继续读下去的特点。限制在300字之内"
    print("\nInvoking LLM for plot summary...")
    try:
        response = llm_with_video_context.invoke(prompt)
        print("\n--- LLM Plot Summary ---")
        print(response)
        print("--- End of Summary ---")
    except Exception as e:
        print(f"Error invoking LLM: {e}")


def test_image_plot_summary(image_file_path: str):

    base64_image = convert_pil_to_base64(Image.open(image_file_path))
    base64_frames = [base64_image]

    print(f"Successfully extracted {len(base64_frames)} frames for the LLM.")

    try:
        llm = OllamaLLM(model="qwen2.5vl:latest")
    except Exception as e:
        print(f"Error initializing OllamaLLM: {e}")
        print("Please ensure Ollama is running and the model 'qwen2.5vl:latest' is pulled (`ollama pull qwen2.5vl:latest`).")
        return

    llm_with_video_context = llm.bind(images=base64_frames)

    prompt = "描述图片中的内容，尽可能的连续、全面。"
    print("\nInvoking LLM for plot summary...")
    try:
        response = llm_with_video_context.invoke(prompt)
        print("\n--- LLM Plot Summary ---")
        print(response)
        print("--- End of Summary ---")
    except Exception as e:
        print(f"Error invoking LLM: {e}")

if __name__ == "__main__":

    # example_video_path = "data/2025/05/18/993667/videos\993667_a382b37a35818941eb83a6cbac6a1ea4.mp4"
    # test_video_plot_summary(example_video_path)
    example_image_path = "D:\\train-material\\20b2a7d9-8082-4a86-a946-8aa0c44aae73.png"
    test_image_plot_summary(example_image_path)

    print("\nReminder: Ensure Ollama is running, 'qwen2.5vl:latest' is pulled, and all Python dependencies are installed.")
    