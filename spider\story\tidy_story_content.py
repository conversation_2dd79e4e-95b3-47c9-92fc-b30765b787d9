import requests
import re

STRAPI_URL="https://storyadmin.poly-ai.chat"
STRAPI_TOKEN="9d1530801a9060d4c63ad3fab40b8da36dfaa354970e0034c218604422b6a4bf59e6c5d23c33c0471e660c088726b150ea2721755afc7f04ba921d573d12ae1cc7de44234fc8b03362599b3435b7fb1f4b49d1313c28de710238c4f877f749a80463470cd7d46fa2f3799492a79ea27a9fc5e665300a6dc3b4a630e747b30f64"


class StoryContentTidier:
    def __init__(self):
        self.headers = {
            "Authorization": f"Bearer {STRAPI_TOKEN}",
            "Content-Type": "application/json"
        }
    
    def fetch_stories(self, page=1, page_size=10):
        """Fetch stories from the Strapi API with pagination"""
        url = f"{STRAPI_URL}/api/stories"
        params = {
            "pagination[page]": page,
            "pagination[pageSize]": page_size,
            "fields": ["id", "title", "content"]
        }
        
        response = requests.get(url, headers=self.headers, params=params)
        response.raise_for_status()
        return response.json()
    
    def tidy_all_stories_paginated(self, page_size=10):
        """Fetch and tidy stories page by page"""
        page = 1
        total_updated = 0
        
        while True:
            print(f"Fetching page {page} (batch size: {page_size})...")
            response_data = self.fetch_stories(page, page_size)
            stories = response_data["data"]
            
            if not stories:  # No more stories to fetch
                print(f"No more stories found on page {page}. Stopping.")
                break
            
            print(f"Fetched {len(stories)} stories from page {page}")
            
            # Process and update stories in this page
            updated_count = 0
            for story in stories:
                story_id = story["id"]
                title = story["attributes"]["title"]
                content = story["attributes"]["content"]
                print(f"Processing story: {title} (ID: {story_id})")
                
                # Clean the content
                cleaned_content = self.clean_content(content)
                # print(f"Original content: {content}")
                # print(f"Cleaned content: {cleaned_content}")

                # Only update if content has changed
                if cleaned_content != content:
                    print(f"Content changed for story '{title}', updating...")
                    self.update_story(story_id, cleaned_content)
                    updated_count += 1
                else:
                    print(f"No changes needed for story '{title}'")
            
            print(f"Updated {updated_count} stories on page {page}")
            total_updated += updated_count
            
            # Check if we have more pages
            pagination_info = response_data.get("meta", {}).get("pagination", {})
            if page >= pagination_info.get("pageCount", 0):
                print(f"No more pages. Processed all {page} pages.")
                break
                
            page += 1

        print(f"Completed! Updated {total_updated} stories across all pages.")
        return total_updated
    
    def clean_content(self, content):
        """Remove extra newlines and whitespace from content"""
        if not content:
            return content
        
        # Replace multiple consecutive newlines with a single newline
        # This pattern matches 2 or more consecutive newlines
        cleaned_content = re.sub(r'\n\s*\n+\s*', '\n\n', content)
        
        # Also remove leading/trailing whitespace
        cleaned_content = cleaned_content.strip()
        
        return cleaned_content
    
    def update_story(self, story_id, content):
        """Update a story's content in the Strapi API"""
        url = f"{STRAPI_URL}/api/stories/{story_id}"
        data = {
            "data": {
                "content": content
            }
        }
        
        response = requests.put(url, headers=self.headers, json=data)
        response.raise_for_status()
        return response.json()
    
    def tidy_all_stories(self):
        """Main method to tidy content for all stories - using paginated approach"""
        print("Fetching and tidying stories with pagination...")
        return self.tidy_all_stories_paginated(page_size=10)

if __name__ == "__main__":
    tidier = StoryContentTidier()
    tidier.tidy_all_stories()
