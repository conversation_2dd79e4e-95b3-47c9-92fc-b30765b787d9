import json
import requests
import traceback
import pathlib
import mimetypes # Added for MIME type detection
from image_compatibility import process_image_to_jpeg_stream
import shutil
import logging

local_file="./data/hot_drama/hotdrama.json"
local_path = "./data/hot_drama"

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')

def append_to_output(new_text):
    """Appends new text to the existing output text."""
    global sync_text
    logging.info(new_text) # Add console logging

    sync_text = sync_text + new_text + "\n"
    sync_arr = sync_text.split("\n")
    sync_text = "\n".join(sync_arr[-50:])  
    return sync_text

def save_local_image(file_path):
    global local_path
    file_name, file_data = process_image_to_jpeg_stream(file_path)
    out_file = f"{local_path}/{file_name}"
    with open(out_file, "wb") as f:
        f.write(file_data)
    return out_file
             
def sync_local_data(day):
    global sync_text
    global local_file
    local_data = {}
    if pathlib.Path(local_file).exists():
        with open(local_file, encoding="utf-8") as f:
            local_data = json.load(f)

    sync_text = ""  # Clear previous output

    data_path = f"./data/{day.replace('-', '/')}/data.json"
    try:
        day_data = json.load(open(data_path, encoding="utf-8"))
    except FileNotFoundError:
        return append_to_output(f"Error: data.json not found for {day}")

    for item in day_data["content"]:
        playlet_id = item["playletId"]
        yield append_to_output(f"Processing playletId: {playlet_id}")

        playlet_info_path = f"./data/{day.replace('-', '/')}/{playlet_id}.json"
        try:
            with open(playlet_info_path, encoding="utf-8") as f:
                playlet_info = json.load(f)
        except FileNotFoundError:
            yield append_to_output(f"Error: {playlet_id}.json not found for playletId {playlet_id}")
            break

        #  Extract data and prepare for POST
        try:
            playlet_name = playlet_info["getPlayletInfo"]["playletName"]
            description = playlet_info["getPlayletInfo"].get("description", "")
            publish_time = playlet_info["getPlayletInfo"].get("releaseStartDate", "")
            playlet_tags = playlet_info["getPlayletInfo"].get("playletTags", [])
            
            actors = []
            if "makingTeam" in playlet_info:
                for actor_info in playlet_info["makingTeam"]:
                    
                    headPic = actor_info["headPic"]
                    headPicPath = save_local_image(headPic)                                
                    actType = ",".join([identity["identity"] for identity in actor_info.get("identityList", [])])
                    act = {
                        "nickname": actor_info["nickname"], 
                        "actType": actType,
                        "headPic":headPicPath
                    }
                    actors.append(act)
            
            cover_oss_local_path = item.get("cover_oss") # Get local cover path from the first JSON
            coverImagePath = save_local_image(cover_oss_local_path)
            coverList = []
            if "materialList" in playlet_info["getPlayletInfo"]:
                for material in playlet_info["getPlayletInfo"]["materialList"]:
                    if type(material) == dict:
                        cover = material["cover"]
                        if cover:
                            coverStrPath = save_local_image(cover)
                            coverList.append(coverStrPath)      
            else:
                yield append_to_output(f"No cover list path found for playletId {playlet_id}")
                continue
            
            if len(description) > 0:
                dramaData = {
                    "playletId": playlet_id,
                    "playletName": playlet_name,
                    "description": description,
                    "publishTime": publish_time,
                    "playletTags": playlet_tags,
                    "actors": actors,
                    "coverList": coverList,
                    "coverImagePath": coverImagePath
                }
                local_data[playlet_id] = dramaData
                      
            yield append_to_output(f"Processing data for playletId {playlet_id} has finieshed.")
        except Exception as e:
            yield append_to_output(f"Error processing data for playletId {playlet_id}, Stack Traceback: {traceback.format_exc()}")

    
    with open(local_file, "w", encoding="utf-8") as f:
        json.dump(local_data, f, ensure_ascii=False, indent=4)
    
        
    
