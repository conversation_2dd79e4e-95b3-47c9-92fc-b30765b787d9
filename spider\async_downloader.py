import asyncio
import aiohttp
import aiofiles
import os
from urllib.parse import urlparse
from logger import logger

class AsyncDownloader:
    def __init__(self, max_concurrent=10):
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    async def download_file(self, session, url, filepath, item_id):
        """异步下载单个文件"""
        async with self.semaphore:
            try:
                async with session.get(url) as response:
                    response.raise_for_status()
                    
                    os.makedirs(os.path.dirname(filepath), exist_ok=True)
                    
                    async with aiofiles.open(filepath, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            await f.write(chunk)
                    
                    logger.info(f"下载成功: {item_id} -> {filepath}")
                    return filepath, f"下载成功: {item_id}"
                    
            except Exception as e:
                logger.error(f"下载失败: {url} - {str(e)}")
                return None, f"下载失败: {url} - {str(e)}"
    
    async def download_batch(self, download_tasks, progress_callback=None):
        """批量异步下载"""
        results = []
        completed = 0
        total = len(download_tasks)
        
        async with aiohttp.ClientSession() as session:
            tasks = []
            for url, filepath, item_id in download_tasks:
                task = self.download_file(session, url, filepath, item_id)
                tasks.append(task)
            
            for coro in asyncio.as_completed(tasks):
                result = await coro
                results.append(result)
                completed += 1
                
                if progress_callback:
                    progress_callback(completed, total)
        
        return results

# 全局下载器实例
downloader = AsyncDownloader()