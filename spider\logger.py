import logging
import os
from datetime import datetime
from config import LOG_FILE, LOG_MAX_LINES

class AppLogger:
    def __init__(self):
        # 确保日志目录存在
        os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(module)s - %(message)s',
            handlers=[
                logging.FileHandler(LOG_FILE, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def info(self, message):
        """记录信息日志"""
        self.logger.info(message)
    
    def error(self, message):
        """记录错误日志"""
        self.logger.error(message)
    
    def warning(self, message):
        """记录警告日志"""
        self.logger.warning(message)
    
    def get_recent_logs(self, lines=LOG_MAX_LINES):
        """获取最近的日志"""
        try:
            if not os.path.exists(LOG_FILE):
                return "日志文件不存在"
            
            with open(LOG_FILE, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                return ''.join(recent_lines)
        except Exception as e:
            return f"读取日志失败: {str(e)}"

# 全局日志实例
logger = AppLogger()