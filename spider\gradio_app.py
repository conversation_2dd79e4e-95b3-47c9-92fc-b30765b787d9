import gradio as gr
import requests
import os
import time
import json
import asyncio
import sqlite3
from datetime import datetime
from urllib.parse import urlparse

from config import *
from logger import logger
from async_downloader import downloader
from summary_online_video import summary_online_video
from summary_test_video import summary_test_video
from sync_online_data import sync_online_data
from sync_test_data import sync_test_data

def create_database():
    """创建数据库和表"""
    conn = sqlite3.connect(DATABASE_NAME)
    cursor = conn.cursor()
    
    cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS entity_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            entity_name TEXT UNIQUE,
            entity_path TEXT
        )
    """)
    
    cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS drama_summary (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            playlet_id TEXT,
            material_id TEXT UNIQUE,
            ad_title TEXT,
            video_url TEXT,
            summary TEXT
        )
    """)
    
    conn.commit()
    conn.close()

def read_headers_from_text(text: str):
    """从文本读取请求头"""
    headers = {}
    for line in text.split("\n"):
        line = line.strip()
        if line and ':' in line:
            key, value = line.split(':', 1)
            headers[key.strip()] = value.strip()
    return headers

def fetch_api_data(url, headers, params=None):
    """获取API数据"""
    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        logger.info(f"API请求成功: {url}")
        return response.json(), f"API请求成功: {url}"
    except Exception as e:
        logger.error(f"API请求失败: {url} - {str(e)}")
        return None, f"API请求失败: {url} - {str(e)}"

def crawl_hot(day, intersection, progress=gr.Progress()):
    """爬取热力榜数据"""
    logger.info("开始爬取热力榜数据")
    progress(0, desc="初始化...")
    
    headers = read_headers_from_text(intersection)
    if not headers:
        return "错误: 无效的请求头"
    
    params = {"pageId": "1", "pageSize": "10", "day": day}
    
    progress(0.2, desc="获取数据...")
    page_data, msg = fetch_api_data(API_URLS["hot_ranking"], headers, params)
    
    if not page_data:
        return msg
    
    # 准备下载任务
    download_tasks = []
    image_save_dir = f"./data/{day.replace('-', '/')}/covers"
    os.makedirs(image_save_dir, exist_ok=True)
    
    progress(0.4, desc="准备下载任务...")
    for item in page_data.get("content", []):
        if isinstance(item, dict):
            playlet_id = item.get("playletId", "")
            image_url = item.get("coverOss", "")
            if image_url and playlet_id:
                filename = f"{playlet_id}_cover.jpg"
                filepath = os.path.join(image_save_dir, filename)
                download_tasks.append((image_url, filepath, playlet_id))
    
    # 异步下载
    if download_tasks:
        progress(0.6, desc="下载图片...")
        
        def download_progress(completed, total):
            progress(0.6 + 0.3 * (completed / total), 
                    desc=f"下载进度: {completed}/{total}")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results = loop.run_until_complete(
            downloader.download_batch(download_tasks, download_progress)
        )
        loop.close()
    
    # 保存数据
    progress(0.9, desc="保存数据...")
    data_save_dir = f"./data/{day.replace('-', '/')}"
    os.makedirs(data_save_dir, exist_ok=True)
    
    with open(f"{data_save_dir}/data.json", "w", encoding="utf-8") as f:
        json.dump(page_data, f, ensure_ascii=False, indent=4)
    
    progress(1.0, desc="完成")
    logger.info("热力榜数据爬取完成")
    return f"热力榜数据爬取完成，保存到: {data_save_dir}/data.json"

def sync_to_environment(day, environment, progress=gr.Progress()):
    """同步数据到指定环境"""
    logger.info(f"开始同步数据到{environment}环境")
    progress(0, desc="初始化同步器...")

    try:
        if environment == "online":
            # 使用线上环境的数据同步功能
            result_generator = sync_online_data(day)
            results = []
            for result in result_generator:
                results.append(result)
                # 简单的进度估算
                progress(len(results) * 0.05, desc=f"同步中: {result[:50]}...")

            final_result = "\n".join(results)
        elif environment == "test":
            # 使用测试环境的数据同步功能
            result_generator = sync_test_data(day)
            results = []
            for result in result_generator:
                results.append(result)
                # 简单的进度估算
                progress(len(results) * 0.05, desc=f"同步中: {result[:50]}...")

            final_result = "\n".join(results)
        else:
            final_result = f"不支持的环境: {environment}"

        progress(1.0, desc="同步完成")
        logger.info(f"{environment}环境同步完成")
        return final_result

    except Exception as e:
        error_msg = f"同步失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

def summary_videos(day, environment, progress=gr.Progress()):
    """AI解析视频摘要"""
    logger.info(f"开始{environment}环境视频AI解析")
    progress(0, desc="初始化AI处理器...")

    try:
        if environment == "online":
            # 使用线上环境的视频摘要功能
            result_generator = summary_online_video(day)
            results = []
            for result in result_generator:
                results.append(result)
                # 简单的进度估算
                progress(len(results) * 0.1, desc=f"处理中: {result[:50]}...")

            final_result = "\n".join(results)
        elif environment == "test":
            # 使用测试环境的视频摘要功能
            result_generator = summary_test_video(day)
            results = []
            for result in result_generator:
                results.append(result)
                # 简单的进度估算
                progress(len(results) * 0.1, desc=f"处理中: {result[:50]}...")

            final_result = "\n".join(results)
        else:
            final_result = f"不支持的环境: {environment}"

        progress(1.0, desc="AI解析完成")
        logger.info(f"{environment}环境视频AI解析完成")
        return final_result

    except Exception as e:
        error_msg = f"AI解析失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

def refresh_logs():
    """刷新日志显示"""
    return logger.get_recent_logs()

def preview_content(file_path):
    """预览文件内容"""
    if not file_path:
        return "请选择文件", gr.update(visible=False), gr.update(visible=False), gr.update(visible=False)
    
    file_extension = os.path.splitext(file_path)[1].lower()
    
    if file_extension in ['.txt', '.json', '.md']:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content, gr.update(visible=True), gr.update(visible=False), gr.update(visible=False)
        except Exception as e:
            return f"读取文件错误: {e}", gr.update(visible=False), gr.update(visible=False), gr.update(visible=False)
    elif file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
        return "", gr.update(visible=False), gr.update(visible=True, value=file_path), gr.update(visible=False)
    elif file_extension in ['.mp4', '.avi', '.mov']:
        return "", gr.update(visible=False), gr.update(visible=False), gr.update(visible=True, value=file_path)
    else:
        return "不支持的文件类型", gr.update(visible=False), gr.update(visible=False), gr.update(visible=False)

def main():
    """创建并启动Gradio界面"""
    create_database()
    
    with gr.Blocks(title="DataEye Crawl UI") as demo:
        gr.Markdown("# DataEye 数据爬取管理系统")
        
        with gr.Row():
            with gr.Column(scale=1):
                day_input = gr.Textbox(
                    label="日期 (YYYY-MM-DD)", 
                    value=get_yesterday(),
                    placeholder="YYYY-MM-DD"
                )
            with gr.Column(scale=3):
                intersection = gr.TextArea(
                    label="抓包信息", 
                    value=DEFAULT_HEADERS,
                    lines=10
                )
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("### 数据爬取")
                crawl_hot_btn = gr.Button("爬取热力榜", variant="primary")
                crawl_page_btn = gr.Button("爬取详情页")
                
            with gr.Column():
                gr.Markdown("### 数据同步")
                sync_test_btn = gr.Button("同步到测试环境")
                sync_online_btn = gr.Button("同步到线上环境")
                
            with gr.Column():
                gr.Markdown("### AI解析")
                summary_test_btn = gr.Button("解析测试环境物料")
                summary_online_btn = gr.Button("解析线上环境物料")
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("### 文件浏览")
                file_browser = gr.FileExplorer(
                    label="文件浏览器", 
                    root_dir="./data", 
                    file_count="single"
                )
                preview_btn = gr.Button("预览文件")
                
            with gr.Column():
                gr.Markdown("### 文件预览")
                preview_text = gr.Textbox(label="文本预览", lines=20, visible=False)
                preview_image = gr.Image(label="图片预览", visible=False)
                preview_video = gr.Video(label="视频预览", visible=False)
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("### 系统日志")
                refresh_log_btn = gr.Button("刷新日志", variant="secondary")
                log_output = gr.Textbox(
                    label="日志输出", 
                    lines=15, 
                    max_lines=30,
                    interactive=False
                )
        
        # 事件绑定
        crawl_hot_btn.click(
            crawl_hot, 
            inputs=[day_input, intersection], 
            outputs=[log_output]
        )
        
        sync_test_btn.click(
            lambda day: sync_to_environment(day, "test"),
            inputs=[day_input],
            outputs=[log_output]
        )
        
        sync_online_btn.click(
            lambda day: sync_to_environment(day, "online"),
            inputs=[day_input],
            outputs=[log_output]
        )
        
        summary_test_btn.click(
            lambda day: summary_videos(day, "test"),
            inputs=[day_input],
            outputs=[log_output]
        )
        
        summary_online_btn.click(
            lambda day: summary_videos(day, "online"),
            inputs=[day_input],
            outputs=[log_output]
        )
        
        refresh_log_btn.click(
            refresh_logs,
            outputs=[log_output]
        )
        
        preview_btn.click(
            preview_content,
            inputs=[file_browser],
            outputs=[preview_text, preview_text, preview_image, preview_video]
        )
        
        # 页面加载时显示最新日志
        demo.load(refresh_logs, outputs=[log_output])
    
    demo.launch(server_name="0.0.0.0", server_port=2342)

if __name__ == "__main__":
    main()
