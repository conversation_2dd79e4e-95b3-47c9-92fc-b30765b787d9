import json
import requests
import traceback
import pathlib
import mimetypes # Added for MIME type detection
from image_compatibility import process_image_to_jpeg_stream
import pymysql
import logging

STRAPI_URL="https://admin.hotdrama.news"
TOKEN="c378b091baa7b1e9a1864e160718209716113b1229c3ea9fd0c617548ae780a8d3588495f8f935b8bb7e618ec2aa5933550fdbd83a0daf47439733838d3551b9bc0fe06cc94ad60bdc6abedd7e5993e77b6c84dd5604bc4d29e10defef029df7bfad90fac414365afcbf036905e55e3071736a28d7e04c685190ffa03ef0dafc"
sync_text = ""

# Configure loggingf
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')

DATABASE_HOST="**************"
DATABASE_PORT="3308"
DATABASE_NAME="hot_drama_cms_db"
DATABASE_USERNAME="cms_user"
DATABASE_PASSWORD="<EMAIL>"

def append_to_output(new_text):
    """Appends new text to the existing output text."""
    global sync_text

    logging.info(new_text) # Add console logging
    sync_text = sync_text + new_text + "\n"
    sync_arr = sync_text.split("\n")
    sync_text = "\n".join(sync_arr[-50:])  
    return sync_text

def init_mysql_connection():
    """Initializes a MySQL database connection."""
    msg = ""
    try:
        connection = pymysql.connect(
            host=DATABASE_HOST,
            port=int(DATABASE_PORT),
            user=DATABASE_USERNAME,
            password=DATABASE_PASSWORD,
            database=DATABASE_NAME,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        msg = f"Connected to MySQL database {DATABASE_NAME} at {DATABASE_HOST}:{DATABASE_PORT} as {DATABASE_USERNAME}"
        return connection, msg 
    except pymysql.MySQLError as e:
        msg = f"Error connecting to database: {traceback.format_exc()}"
        return None, msg

def query_sql(conn, sql):
    """Executes a SQL query and returns the result."""
    msg = ""
    try:
        with conn.cursor() as cursor:
            cursor.execute(sql)
            result = cursor.fetchall()
            msg = f"Executed query: {sql} - Rows returned: {len(result)}"
            return result, msg
    except pymysql.MySQLError as e:
        msg = f"Error executing query: {traceback.format_exc()}"
        return None, msg

def execute_sql(conn, sql):
    """Executes a SQL command and returns the number of affected rows."""
    try:
        with conn.cursor() as cursor:
            cursor.execute(sql)
            conn.commit()
            msg = f"Executed command: {sql} - Rows affected: {cursor.rowcount}"
            return cursor.rowcount, msg
    except pymysql.MySQLError as e:
        msg = f"Error executing command: {traceback.format_exc()}"
        conn.rollback()
        return None, msg

def correct_relationship(playlet_id, image_id):
    conn, msg = init_mysql_connection()
    
    if conn:
        try:
            sql="select id from episodes where playlet_id = {}".format(playlet_id)
            episodes, msg = query_sql(conn, sql)
            if not episodes:
                return msg 
            
            sql="select * from files_related_mph where file_id = {}".format(image_id)
            files_mph, msg = query_sql(conn, sql)

            relation = set()
            for mph in files_mph:
                relation.add(f"{mph["related_type"]},{mph["field"]},{mph["order"]}")
            msgs = []                
            for row in episodes:
                episode_id = row["id"]
                found = False
                for mph in files_mph:
                    if mph["related_id"] == episode_id:
                        found = True
                        break
                if not found:
                    for rel in relation:
                        rel_arr = rel.split(",")
                        sql="insert into files_related_mph (file_id, related_id, related_type, field, `order`) values \
                        ({}, {}, '{}', '{}', {})".format(image_id, episode_id, rel_arr[0], rel_arr[1], rel_arr[2])
                        rowcnt, msg = execute_sql(conn, sql)
                        msgs.append(msg)
            if len(msgs) > 0:
                return "\n".join(msgs)
            else:
                return f"No relationships need correct for playlet_id {playlet_id} with image_id {image_id}."
        except Exception as e:
            msg = f"Error executing query: {traceback.format_exc()}"
            return msg
        finally:
            conn.close()
    else:
        return msg 
        
def upload_image(file_path, eid = None, field = None, table = "api::episode.episode"):
    """
    上传故事图片并返回响应。
    """
    msg = ""
    try:
        file_name, file_content = process_image_to_jpeg_stream(file_path)
        mime_type, _ = mimetypes.guess_type(file_name)
        image_data = get_image(file_name)
        
        if len(image_data) > 0:
            msg = f"Found Image {file_name} in strapi cms backend."
            return image_data, msg
        
        if not mime_type:
            mime_type = 'application/octet-stream' 

        # 准备请求数据
        files = {'files': (file_name, file_content, mime_type)} # Use determined MIME type

        header = {
            "Authorization": f"Bearer {TOKEN}"
        }
        
        if eid:
            data = {
                    "ref": table,
                    "refId":eid,
                    "field": field
                }
        else:
            data = {}
            
        response = requests.post(f'{STRAPI_URL}/api/upload', headers=header, files=files, data=data)
                        
        # 检查响应
        if response.ok:
            msg = f"文件上传成功: {file_name}"
            return response.json(), msg
        else:
            msg = f"文件上传失败: {response.status_code}"
            return response.text, msg
    except Exception as e:
        msg = f"上传文件时发生错误: {traceback.format_exc()}"
        return None, msg

def get_image(name): 
    header = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    r = requests.get(f"{STRAPI_URL}/api/upload/files?populate=*&filters[name][$eq]={name}", headers=header) 
    return r.json()

def create_episodes(data):
    msg = ""
    try:
        # Prepare the data for Strapi, ensuring it's wrapped in a 'data' key
        strapi_payload = {"data": data}

        header = {
            "Authorization": f"Bearer {TOKEN}",
            "Content-Type": "application/json"
        }
        response = requests.post(f"{STRAPI_URL}/api/episodes", headers=header, json=strapi_payload)
        response.raise_for_status()
        msg = f"Successfully created episodes for playletId {data['playlet_id']}, {strapi_payload}"
        return response.json(), msg
        
    except Exception as e:
        msg = f"Error posting data for playletId {data["playlet_id"]}: {traceback.format_exc()}"
        return None, msg
    
def update_episodes(documentId, data):
    msg = ""
    try:
        # Prepare the data for Strapi, ensuring it's wrapped in a 'data' key
    
        strapi_payload = {"data": data}

        header = {
            "Authorization": f"Bearer {TOKEN}",
            "Content-Type": "application/json"
        }
        response = requests.put(f"{STRAPI_URL}/api/episodes/{documentId}", headers=header, json=strapi_payload)
        response.raise_for_status() 
        msg = f"Successfully updated episodes for documentId {documentId}"
        return response.json(), msg
        
    except requests.exceptions.RequestException as e:
        msg = f"Error update data for {documentId} : {traceback.format_exc()}"
        return None, msg

def find_episodes(data):
    msg = ""
    try:
        header = {
            "Authorization": f"Bearer {TOKEN}",
            "Content-Type": "application/json"
        }
        response = requests.get(f"{STRAPI_URL}/api/episodes?populate=*&filters[playlet_id][$eq]={data['playlet_id']}", headers=header)
        response.raise_for_status() 
        msg = f"Successfully retrieved episodes for playletId {data['playlet_id']}, found {len(response.json()['data'])} records."
        return response.json(), msg
    
    except requests.exceptions.RequestException as e:
        msg = f"Error get data for {data["playlet_id"]} : {traceback.format_exc()}"
        return None, msg


def sync_online_data(day):
    global sync_text
    sync_text = ""  # Clear previous output

    data_path = f"./data/{day.replace('-', '/')}/data.json"
    try:
        day_data = json.load(open(data_path, encoding="utf-8"))
    except FileNotFoundError:
        return append_to_output(f"Error: data.json not found for {day}")

    total = len(day_data["content"])
    processed = 0
    for item in day_data["content"]:
        playlet_id = item["playletId"]
        yield append_to_output(f"Start Processing playletId: {playlet_id}")

        playlet_info_path = f"./data/{day.replace('-', '/')}/{playlet_id}.json"
        try:
            with open(playlet_info_path, encoding="utf-8") as f:
                playlet_info = json.load(f)
        except FileNotFoundError:
            yield append_to_output(f"Error: {playlet_id}.json not found for playletId {playlet_id}")
            break

        #  Extract data and prepare for POST
        try:
            playlet_name = playlet_info["getPlayletInfo"]["playletName"]
            description = playlet_info["getPlayletInfo"].get("description", "")
            publish_time = playlet_info["getPlayletInfo"].get("releaseStartDate", "")
            playlet_tags = playlet_info["getPlayletInfo"].get("playletTags", [])
            
            data = {
                "playlet_id": str(playlet_id),
                "publish_time": publish_time,
                "name": playlet_name,
                "description": description if len(description) > 0 else playlet_name,
                "keywords": playlet_tags,
                "category": playlet_tags[0] if playlet_tags else "未知"
            }
                        
            episodes, msg = find_episodes(data)
            yield append_to_output(msg)
            if episodes and len(episodes["data"]) == 0:
                episodes, msg = create_episodes(data)
                yield append_to_output(msg)
                episodes, msg = find_episodes(data)
                yield append_to_output(msg)
            if not episodes or "data" not in episodes or len(episodes["data"]) == 0:
                yield append_to_output(f"No episodes found for playletId {playlet_id}, skipping...")
                continue

            documentId = episodes["data"][0]["documentId"]
            episodes, msg = update_episodes(documentId, data)
            yield append_to_output(msg)
            if not episodes:
                continue

            actors = []
            if "makingTeam" in playlet_info:
                for actor_info in playlet_info["makingTeam"]:
                    
                    headPic = actor_info["headPic"]
                    headPicPath = ""
                    if headPic:
                        upload_response, msg = upload_image(headPic)
                        yield append_to_output(msg)
                        if upload_response:
                            if type(upload_response) == list:
                                upload_response = upload_response[0]
                            else:
                                upload_response = upload_response["data"]
                                
                            headPicPath = upload_response["url"]
                            headPicId = upload_response["id"]
                            yield append_to_output(f"Cover uploaded successfully. Image ID: {headPicId}")
                        else:
                            yield append_to_output(f"Failed to upload cover or get ID. Response: {upload_response}")
                                                    
                    actType = ",".join([identity["identity"] for identity in actor_info.get("identityList", [])])
                    act = {
                        "nickname": actor_info["nickname"], 
                        "actType": actType,
                        "headPic":headPicPath
                    }
                    actors.append(act)
            
            if len(actors) > 0:
                data["actor"] = json.dumps(actors)
                episodes, msg = update_episodes(documentId, data)
                yield append_to_output(msg)
                
            episodes, msg = find_episodes(data)

            yield append_to_output(msg)
            eid = episodes["data"][0]["id"]
            cover_oss_local_path = item.get("cover_oss") # Get local cover path from the first JSON

            if cover_oss_local_path:
                yield append_to_output(f"Attempting to upload cover: {cover_oss_local_path}")
                upload_response, msg = upload_image(cover_oss_local_path, eid, "cover")
                yield append_to_output(msg)

                if upload_response:
                    if type(upload_response) == list:
                        upload_response = upload_response[0]
                    else:
                        upload_response = upload_response["data"]
                    msg = correct_relationship(playlet_id, upload_response["id"])
                    yield append_to_output(msg)
                else:
                    yield append_to_output(f"Failed to upload cover or get ID. Response: {upload_response}")
            else:
                yield append_to_output(f"No local cover path (cover_oss) found for playletId {playlet_id}")
                continue
            
            if "materialList" in playlet_info["getPlayletInfo"]:
                for material in playlet_info["getPlayletInfo"]["materialList"]:
                    if type(material) == dict:
                        cover = material["cover"]
                        if cover:
                            yield append_to_output(f"Attempting to upload coverlist: {cover}")
                            upload_response, msg = upload_image(cover, eid, "coverlist")
                            yield append_to_output(msg)
                            if upload_response:
                                if type(upload_response) == list:
                                    upload_response = upload_response[0]
                                else:
                                    upload_response = upload_response["data"]
                                    
                                msg = correct_relationship(playlet_id, upload_response["id"])  
                                yield append_to_output(msg)
                            else:
                                yield append_to_output(f"Failed to upload cover or get ID. Response: {upload_response}")
            else:
                yield append_to_output(f"No cover list path found for playletId {playlet_id}")
                continue
            yield append_to_output(f"Processing data for playletId {playlet_id} has finieshed.")
            processed += 1

        except Exception as e:
            yield append_to_output(f"Error processing data for playletId {playlet_id}, Stack Traceback: {traceback.format_exc()}")

    yield append_to_output(f"process {day_data} finished. total {total}, processed success {processed}.")        
    
        
    
