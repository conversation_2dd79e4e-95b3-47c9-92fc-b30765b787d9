<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HotDrama</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #1a1a1a; /* 深色背景 */
            color: #e0e0e0; /* 浅色文字 */
        }
        .carousel-container {
            overflow-x: hidden; /* 隐藏横向滚动条 */
            white-space: nowrap; /* 防止图片换行 */
            -webkit-overflow-scrolling: touch; /* 改善移动端滚动体验 */
        }
        .carousel-item {
            display: inline-block; /* 使图片排成一行 */
            margin-right: 8px; /* 图片之间的间距 */
            width: 120px; /* 轮播图项的宽度 */
            height: 180px; /* 轮播图项的高度 */
            object-fit: cover; /* 保持图片比例并填充容器 */
            border-radius: 8px; /* 圆角 */
            transition: transform 0.5s ease-in-out; /* 添加过渡效果 */
        }
         .header-carousel-item {
            display: inline-block; /* 使图片排成一行 */
            margin-right: 8px; /* 图片之间的间距 */
            width: 200px; /* 头部轮播图项的宽度 */
            height: 300px; /* 头部轮播图项的高度 */
            object-fit: cover; /* 保持图片比例并填充容器 */
            border-radius: 8px; /* 圆角 */
            transition: transform 0.5s ease-in-out; /* 添加过渡效果 */
        }
         .header-carousel-wrapper, .detail-carousel-wrapper {
            display: flex; /* 使用 flex 布局 */
            transition: transform 0.5s ease-in-out; /* 添加过渡效果 */
         }

        /* 隐藏详情页，默认显示主页 */
        #detail-page {
            display: none;
        }

        /* 剧集列表项在所有屏幕尺寸下都使用 flex 布局，实现单列效果 */
        .drama-list-item {
             display: flex;
             background-color: #1f2937; /* bg-gray-800 */
             border-radius: 8px; /* rounded-lg */
             padding: 16px; /* p-4 */
             cursor: pointer;
             transition: background-color 0.2s ease-in-out; /* hover:bg-gray-700 */
             align-items: flex-start; /* 顶部对齐 */
        }

        .drama-list-item:hover {
             background-color: #374151; /* hover:bg-gray-700 */
        }

        .drama-list-item img {
             flex-shrink: 0; /* 图片不缩小 */
             width: 96px; /* w-24 */
             height: 144px; /* h-36 */
             object-fit: cover;
             border-radius: 8px;
             margin-right: 16px; /* mr-4 */
        }

        .drama-list-item .info {
             flex-grow: 1; /* 信息区域填充剩余空间 */
        }

        .drama-list-item h3 {
             font-size: 1.125rem; /* text-lg */
             font-weight: bold;
             margin-bottom: 4px; /* mb-1 */
        }

        .drama-list-item p {
             color: #9ca3af; /* text-gray-400 */
             font-size: 0.875rem; /* text-sm */
             margin-top: 4px; /* mt-1 */
        }

         /* 优化分页按钮样式 */
        .pagination {
             display: flex;
             justify-content: center;
             align-items: center;
             margin-top: 2rem; /* mt-8 */
        }

        .pagination button {
             padding: 0.5rem 1rem; /* 8px 16px */
             margin: 0 0.25rem; /* 0 4px */
             background-color: #374151; /* bg-gray-700 */
             color: #d1d5db; /* text-gray-300 */
             border-radius: 0.5rem; /* rounded-md */
             font-size: 0.875rem; /* text-sm */
             cursor: pointer;
             transition: background-color 0.2s ease-in-out;
             border: none; /* 移除默认边框 */
        }

        .pagination button:hover:not(:disabled) {
             background-color: #4b5563; /* hover:bg-gray-600 */
        }

        .pagination button:disabled {
             opacity: 0.5;
             cursor: not-allowed;
        }

        .pagination button.active {
             background-color: #f97316; /* bg-orange-500 */
             color: white;
        }

         /* 优化省略号垂直对齐 */
        .pagination span {
             display: inline-flex; /* 使用 flexbox 确保垂直居中 */
             align-items: center;
             padding: 0.5rem 0.5rem; /* 调整内边距 */
             color: #9ca3af; /* text-gray-400 */
             font-size: 0.875rem; /* text-sm */
        }
        .detail-carousel-item {
            display: inline-block;
            margin-right: 8px;
            width: 240px; /* Explicitly set width based on Tailwind w-60 */
            height: 320px; /* Explicitly set height based on Tailwind h-80 */
            object-fit: cover;
            border-radius: 8px;
            transition: transform 0.5s ease-in-out;
        }        
        .detail-carousel-item[src^="https://placehold.co"] {  /* Style placeholder images */
            border: 2px dashed #f97316; /* Orange dashed border for placeholders */
          }
          
    </style>
</head>
<body class="p-4">

    <div id="main-page">
        <header class="flex justify-between items-center mb-6">
            <div class="text-2xl font-bold text-orange-500">HotDrama</div>
            <div class="flex items-center">
                <input type="text" placeholder="搜索..." class="px-3 py-1 rounded-full bg-gray-700 text-white text-sm focus:outline-none focus:ring-2 focus:ring-orange-500">
                <button class="ml-2 text-orange-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>
            </div>
        </header>

        <nav class="mb-6">
            <ul id="dynamic-nav-tags" class="flex space-x-4 overflow-x-auto pb-2">
                <!-- 导航标签将由 JavaScript 动态生成 -->
            </ul>
        </nav>

        <div class="max-w-md mx-auto">
            <section id="header-carousel-container" class="mb-8 carousel-container">
                 <div id="header-carousel-wrapper" class="header-carousel-wrapper">
                     </div>
            </section>

            <section id="drama-list">
                <h2 id="drama-list-title" class="text-xl font-bold mb-4">全部剧集</h2>
                <div id="all-drama-list" class="flex flex-col gap-6">
                    </div>
                 <div id="pagination" class="flex justify-center mt-8 pagination">
                    </div>
            </section>
        </div>
    </div>

    <div id="detail-page">
        <button id="back-button" class="mb-4 px-4 py-2 bg-gray-700 text-white rounded-full text-sm hover:bg-gray-600">← 返回</button>

        <div class="max-w-md mx-auto">
            <div id="detail-carousel-container" class="carousel-container mb-6">
                <div id="detail-carousel-wrapper" class="detail-carousel-wrapper">
                </div>
            </div>
            <section id="detail-content">
            </section>
        </div>
    </div>


    <script>
        let hotDramaData = {}; // 声明一个变量来存储加载的数据
        let currentPage = 1; // 当前页码
        const itemsPerPage = 10; // 每页显示的剧集数量
        let headerCarouselInterval; // 用于存储头部轮播图的定时器
        let detailCarouselInterval; // 用于存储详情页轮播图的定时器
        let currentFilterTag = '全部'; // 新增：用于存储当前激活的过滤标签，默认为“全部”
        let currentSearchTerm = ''; // 新增：用于存储当前搜索关键词

        // 获取页面元素
        const mainPage = document.getElementById('main-page');
        const detailPage = document.getElementById('detail-page');
        const headerCarouselContainer = document.getElementById('header-carousel-container'); // 头部轮播图容器
        const headerCarouselWrapper = document.getElementById('header-carousel-wrapper'); // 头部轮播图实际包裹层
        const detailCarouselContainer = document.getElementById('detail-carousel-container'); // 详情页轮播图容器
        const detailCarouselWrapper = document.getElementById('detail-carousel-wrapper'); // 详情页轮播图实际包裹层
        const allDramaList = document.getElementById('all-drama-list');
        const detailContent = document.getElementById('detail-content');
        const backButton = document.getElementById('back-button');
        const paginationContainer = document.getElementById('pagination'); // 分页容器
        const dynamicNavTagsList = document.getElementById('dynamic-nav-tags'); // 新增：获取导航ul元素
        const searchInput = document.querySelector('header input[type="text"]'); // 新增：获取搜索输入框
        const searchButton = document.querySelector('header button'); // 新增：获取搜索按钮 (旁边的放大镜图标)
        const dramaListTitle = document.getElementById('drama-list-title'); // 新增：获取剧集列表标题元素

        // 新增：渲染动态导航标签的函数
        function renderNavigationTabs(tags) {
            if (!dynamicNavTagsList) {
                console.error("Navigation UL element 'dynamic-nav-tags' not found!");
                return;
            }
            dynamicNavTagsList.innerHTML = ''; // 清空现有标签

            tags.forEach(tag => {
                const li = document.createElement('li');
                const button = document.createElement('button');
                button.textContent = tag;
                button.classList.add('px-4', 'py-2', 'rounded-full', 'text-sm', 'whitespace-nowrap'); // whitespace-nowrap 防止标签文字换行

                if (tag === currentFilterTag) {
                    button.classList.add('bg-orange-600', 'text-white');
                } else {
                    button.classList.add('bg-gray-700', 'text-gray-300', 'hover:bg-gray-600');
                }

                button.addEventListener('click', () => {
                    currentFilterTag = tag;
                    currentPage = 1; // 切换标签时重置到第一页
                    renderMainPage();
                    updateActiveNavButtonStates(); // 更新所有导航按钮的激活状态
                });

                li.appendChild(button);
                dynamicNavTagsList.appendChild(li);
            });
        }

        // 新增：更新导航按钮激活状态的函数
        function updateActiveNavButtonStates() {
            if (!dynamicNavTagsList) return;
            const buttons = dynamicNavTagsList.querySelectorAll('button');
            buttons.forEach(button => {
                button.classList.remove('bg-orange-600', 'text-white');
                button.classList.add('bg-gray-700', 'text-gray-300', 'hover:bg-gray-600');
                if (button.textContent === currentFilterTag) {
                    button.classList.remove('bg-gray-700', 'text-gray-300', 'hover:bg-gray-600');
                    button.classList.add('bg-orange-600', 'text-white');
                }
            });
        }
        
        // 修改：渲染主页函数，以支持过滤和搜索
        function renderMainPage() {
            mainPage.style.display = 'block';
            detailPage.style.display = 'none';
            allDramaList.innerHTML = ''; 
            paginationContainer.innerHTML = ''; 
            if (detailCarouselInterval) clearInterval(detailCarouselInterval);

            // --- 1. 准备并渲染头部轮播图数据 (始终来自全部剧集) ---
            // ... (这部分不变) ...
            const allDramasForHeader = Object.values(hotDramaData);
            const sortedAllDramasForHeader = allDramasForHeader.sort((a, b) => new Date(b.publishTime) - new Date(a.publishTime));
            const recentDramasForHeader = sortedAllDramasForHeader.slice(0, 5);

            headerCarouselWrapper.innerHTML = ''; 
            if (headerCarouselInterval) clearInterval(headerCarouselInterval);

            recentDramasForHeader.forEach(drama => {
                const imgElement = document.createElement('img');
                imgElement.src = drama.coverImagePath;
                imgElement.alt = drama.playletName;
                imgElement.classList.add('header-carousel-item');
                imgElement.addEventListener('click', () => navigateToDetail(drama.playletId));
                headerCarouselWrapper.appendChild(imgElement);
            });
            if (recentDramasForHeader.length > 0) { 
                headerCarouselInterval = startCarousel(headerCarouselWrapper, 'header-carousel-item', 1500);
            }


            // --- 2. 准备主列表的剧集数据 (根据 currentFilterTag 和 currentSearchTerm 过滤) ---
            let dramasForList = Object.values(hotDramaData);

            // 首先按标签过滤
            if (currentFilterTag && currentFilterTag !== '全部') {
                dramasForList = dramasForList.filter(drama => drama.playletTags && drama.playletTags.includes(currentFilterTag));
            }

            // 然后按搜索关键词过滤 (不区分大小写)
            if (currentSearchTerm) {
                dramasForList = dramasForList.filter(drama => 
                    drama.playletName && drama.playletName.toLowerCase().includes(currentSearchTerm)
                );
            }
            
            // 按发布时间排序剧集数据
            const sortedDramasForList = dramasForList.sort((a, b) => new Date(b.publishTime) - new Date(a.publishTime));

            // --- 2.1 更新剧集列表标题 ---
            if (dramaListTitle) {
                if (currentSearchTerm) {
                    dramaListTitle.textContent = `搜索 "${currentSearchTerm}" 的结果`;
                    if (currentFilterTag !== '全部') {
                        dramaListTitle.textContent += ` (标签: ${currentFilterTag})`;
                    }
                } else if (currentFilterTag !== '全部') {
                    dramaListTitle.textContent = `"${currentFilterTag}" 类剧集`;
                } else {
                    dramaListTitle.textContent = '全部剧集';
                }
            }

            // --- 3. 渲染主剧集列表 ---
            if (sortedDramasForList.length === 0) {
                let noResultsMessage = '';
                if (currentSearchTerm && currentFilterTag !== '全部') {
                    noResultsMessage = `没有找到符合标签 "${currentFilterTag}" 且名称包含 "${currentSearchTerm}" 的剧集。`;
                } else if (currentSearchTerm) {
                    noResultsMessage = `没有找到名称包含 "${currentSearchTerm}" 的剧集。`;
                } else if (currentFilterTag !== '全部') {
                    noResultsMessage = `没有找到符合 "${currentFilterTag}" 标签的剧集。`;
                } else {
                    noResultsMessage = '暂无剧集数据。';
                }
                allDramaList.innerHTML = `<p class="text-center text-gray-400 mt-8">${noResultsMessage}</p>`;
                paginationContainer.innerHTML = ''; 
                return; 
            }

            // ... (计算总页数、获取当前页数据、渲染列表项的代码不变) ...
            const totalItems = sortedDramasForList.length;
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const dramasToDisplay = sortedDramasForList.slice(startIndex, endIndex);

            dramasToDisplay.forEach(drama => {
                const dramaElement = document.createElement('div');
                dramaElement.classList.add('drama-list-item');
                dramaElement.dataset.playletId = drama.playletId;

                const actorsHtml = drama.actors && drama.actors.length > 0
                    ? `<span class="text-xs text-gray-500 mt-1">演职人员: ${drama.actors.map(actor => actor.nickname + '(' + actor.actType + ')').join(', ')}</span>`
                    : '<span class="text-xs text-gray-500 mt-1">演职人员: 无演员信息</span>';
                const recommendHtml = drama.recommend
                ? `<p>${drama.recommend}</p>`
                : '<p>无推荐信息</p>';

                const keywordsHtml = drama.keywords && Array.isArray(drama.keywords) && drama.keywords.length > 0 // Added Array.isArray()
                ? `<div class="flex flex-wrap gap-2 mb-2 mt-2">
                    ${drama.keywords.map(keyword => `<span class="px-2 py-1 bg-orange-500 rounded-full text-xs text-white">${keyword}</span>`).join('')}
                </div>`
                : '<div class="flex flex-wrap gap-2 mb-2 mt-2"><span class="px-2 py-1 bg-gray-600 rounded-full text-xs">无爆点词</span></div>';

                dramaElement.innerHTML = `
                    <img src="${drama.coverImagePath}" alt="${drama.playletName} 主图" onerror="this.onerror=null;this.src='https://placehold.co/96x144/374151/d1d5db?text=No+Image';">
                    <div class="info">
                        <h3>${drama.playletName}</h3>
                        ${actorsHtml}
                        <div class="text-sm text-gray-400 mt-2 mb-2">
                            ${recommendHtml}
                        </div>
                        ${keywordsHtml}
                    </div>
                `;
                
                dramaElement.addEventListener('click', () => navigateToDetail(drama.playletId));
                allDramaList.appendChild(dramaElement);
            });

            renderPagination(totalPages);
        }

        // 通用的轮播图启动函数
        function startCarousel(wrapperElement, itemClass, intervalTime) {
             let currentIndex = 0;
             const items = wrapperElement.children;
             const totalItems = items.length;

             if (totalItems <= 1) {
                 // 如果剧集少于或等于1，无需轮播
                 return;
             }

             // 复制第一张图片到末尾，实现无缝轮播效果
             const firstItemClone = items[0].cloneNode(true);
             wrapperElement.appendChild(firstItemClone);


             const intervalId = setInterval(() => {
                 currentIndex++;
                  // 计算偏移量，包括图片宽度和 margin-right
                 const itemWidth = items[0].offsetWidth + parseInt(getComputedStyle(items[0]).marginRight);
                 wrapperElement.style.transform = `translateX(${-currentIndex * itemWidth}px)`;

                 // 当到达复制的第一个图片时，瞬间跳回真正的第一个图片，实现无缝效果
                 if (currentIndex === totalItems) {
                     setTimeout(() => {
                         wrapperElement.style.transition = 'none'; // 暂时移除过渡效果
                         currentIndex = 0;
                         wrapperElement.style.transform = `translateX(0)`;
                         // 强制浏览器重新计算样式，以便下一次轮播有过渡效果
                         wrapperElement.offsetHeight;
                         wrapperElement.style.transition = 'transform 0.5s ease-in-out'; // 恢复过渡效果
                     }, 500); // 这里的 500ms 应该与 CSS 中的过渡时间一致
                 }

             }, intervalTime); // 切换间隔时间

             return intervalId; // 返回定时器ID以便清除
        }


        // 渲染分页按钮
        function renderPagination(totalPages) {
            paginationContainer.innerHTML = ''; // 清空现有分页按钮

            // 上一页按钮
            const prevButton = document.createElement('button');
            prevButton.textContent = '<';
            prevButton.disabled = currentPage === 1;
            prevButton.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    renderMainPage(); // 重新渲染主页内容
                }
            });
            paginationContainer.appendChild(prevButton);

            // 页码按钮 (只显示部分页码，例如当前页、前后两页以及首尾页)
            const maxPageButtons = 5; // 最多显示的页码按钮数量
            let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
            let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);

             // 调整 startPage 和 endPage，确保显示 maxPageButtons 个按钮
            if (endPage - startPage + 1 < maxPageButtons) {
                 startPage = Math.max(1, endPage - maxPageButtons + 1);
            }


            if (startPage > 1) {
                 const firstPageButton = document.createElement('button');
                 firstPageButton.textContent = 1;
                 firstPageButton.addEventListener('click', () => {
                     currentPage = 1;
                     renderMainPage();
                 });
                 paginationContainer.appendChild(firstPageButton);
                 if (startPage > 2) {
                     const ellipsis = document.createElement('span');
                     ellipsis.textContent = '...';
                     ellipsis.classList.add('px-2', 'py-1', 'text-gray-400');
                     paginationContainer.appendChild(ellipsis);
                 }
            }


            for (let i = startPage; i <= endPage; i++) {
                const pageButton = document.createElement('button');
                pageButton.textContent = i;
                if (i === currentPage) {
                    pageButton.classList.add('active');
                }
                pageButton.addEventListener('click', () => {
                    currentPage = i;
                    renderMainPage(); // 重新渲染主页内容
                });
                paginationContainer.appendChild(pageButton);
            }

            if (endPage < totalPages) {
                 if (endPage < totalPages - 1) {
                     const ellipsis = document.createElement('span');
                     ellipsis.textContent = '...';
                      ellipsis.classList.add('px-2', 'py-1', 'text-gray-400');
                     paginationContainer.appendChild(ellipsis);
                 }
                 const lastPageButton = document.createElement('button');
                 lastPageButton.textContent = totalPages;
                 lastPageButton.addEventListener('click', () => {
                     currentPage = totalPages;
                     renderMainPage();
                 });
                 paginationContainer.appendChild(lastPageButton);
            }


            // 下一页按钮
            const nextButton = document.createElement('button');
            nextButton.textContent = '>';
            nextButton.disabled = currentPage === totalPages;
            nextButton.addEventListener('click', () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    renderMainPage(); // 重新渲染主页内容
                }
            });
            paginationContainer.appendChild(nextButton);
        }


        // 渲染详情页
        function renderDetailPage(playletId) {
            mainPage.style.display = 'none';
            detailPage.style.display = 'block';
            detailContent.innerHTML = ''; // 清空详情页内容，除了轮播图容器
            detailCarouselWrapper.innerHTML = ''; // 清空详情页轮播图内容
            clearInterval(headerCarouselInterval); // 清除主页的轮播定时器
            clearInterval(detailCarouselInterval); // 清除详情页可能存在的轮播定时器


            const drama = hotDramaData[playletId];

            if (!drama) {
                detailContent.innerHTML = `<p class="text-red-500">未找到该剧集详情。</p>`;
                return;
            }

             // 填充详情页轮播图
            drama.coverList.forEach(coverUrl => {
                const imgElement = document.createElement('img');
                imgElement.src = coverUrl;
                imgElement.alt = drama.playletName + ' 轮播图';
                imgElement.classList.add('detail-carousel-item'); // Use new class
                imgElement.onerror = function() { this.onerror=null;this.src='https://placehold.co/240x320/374151/d1d5db?text=No+Image'; };
                detailCarouselWrapper.appendChild(imgElement);
            });
            
             // 启动详情页轮播图自动播放
            detailCarouselInterval = startCarousel(detailCarouselWrapper, 'detail-carousel-item', 1500); 

             // 演员信息字符串
            const actorsDetailHtml = drama.actors && drama.actors.length > 0
                ? `<span class="text-xs text-gray-500 mt-1"">演职人员: ${drama.actors.map(actor => actor.nickname + '(' + actor.actType + ')').join(', ')}</span>`
                : '<span class="text-xs text-gray-500 mt-1">演职人员:无演员信息"></span>';

            // 剧集详细信息 (不包含轮播图部分)
            const dramaInfoHtml = `
                <h2 class="text-2xl font-bold mb-2">${drama.playletName}</h2>
                <p class="text-gray-300 mb-4 mt-2">${drama.recommend || '暂无推荐信息'}</p>
                <div class="flex flex-wrap gap-2 mb-4"> 
                    ${(drama.keywords && Array.isArray(drama.keywords) ? drama.keywords : ["无爆点词"]).map(keyword => `<span class="px-2 py-1 bg-orange-600 text-xs text-white rounded-full">${keyword}</span>`).join('')}
                </div>                
                <img src="${drama.coverImagePath}" alt="${drama.playletName} 主图" onerror="this.onerror=null;this.src='https://placehold.co/96x144/374151/d1d5db?text=No+Image';">
                <p class="text-gray-500 text-sm mb-4">发布日期: ${drama.publishTime}</p>
                ${actorsDetailHtml}
                <div class="flex flex-wrap gap-2">
                    ${drama.playletTags.map(tag => `<span class="px-2 py-1 bg-orange-600 rounded-full text-xs">${tag}</span>`).join('')}
                </div>
                <button class="px-6 py-3 bg-orange-500 text-white rounded-full text-lg hover:bg-orange-600 gap-2 mt-2 mb-2">▶ 立即观看</button>
                <p class="text-gray-300 mb-4 mt-10">剧情简介:${drama.description || '暂无描述'}</p>
                 
            `;

            // 相关推荐区域
            const relatedRecommendationsHtml = `
                <h3 class="text-xl font-bold mt-8 mb-4">相关推荐</h3>
                <div id="related-dramas-list" class="grid grid-cols-2 gap-4">
                    </div>
            `;

            // 将非轮播图内容添加到 detailContent
            detailContent.innerHTML +=  dramaInfoHtml + relatedRecommendationsHtml;


            // 填充相关推荐
            renderRelatedRecommendations(playletId);
        }

        // 渲染相关推荐
        function renderRelatedRecommendations(currentPlayletId) {
            const relatedDramasList = document.getElementById('related-dramas-list');
            relatedDramasList.innerHTML = ''; // 清空相关推荐列表

            const allDramas = Object.values(hotDramaData);
            // 过滤掉当前剧集
            const otherDramas = allDramas.filter(drama => drama.playletId !== currentPlayletId);

            // 随机选取5个剧集
            const shuffledDramas = otherDramas.sort(() => 0.5 - Math.random()); // 随机打乱数组
            const relatedDramas = shuffledDramas.slice(0, 5); // 取前5个

            relatedDramas.forEach(drama => {
                 const dramaElement = document.createElement('div');
                 dramaElement.classList.add('bg-gray-800', 'rounded-lg', 'p-2', 'flex', 'flex-col', 'cursor-pointer', 'hover:bg-gray-700');
                 dramaElement.dataset.playletId = drama.playletId; // 存储剧集 ID

                 dramaElement.innerHTML = `
                     <img src="${drama.coverImagePath}" alt="${drama.playletName} 主图" class="w-full h-40 object-cover rounded-lg mb-2" onerror="this.onerror=null;this.src='https://placehold.co/160x160/374151/d1d5db?text=No+Image';">
                     <h4 class="text-sm font-bold line-clamp-2">${drama.playletName}</h4>
                 `;

                 // 添加点击事件，跳转到详情页
                 dramaElement.addEventListener('click', () => {
                     navigateToDetail(drama.playletId);
                 });

                 relatedDramasList.appendChild(dramaElement);
            });
        }


        // 导航到详情页
        function navigateToDetail(playletId) {
            renderDetailPage(playletId);
        }

        // 返回主页 (不变, 但 renderMainPage 会使用 currentFilterTag)
        backButton.addEventListener('click', () => {
            // currentPage = 1; // 重置页码通常在切换过滤器或首次加载时进行
            renderMainPage();
            updateActiveNavButtonStates(); // 确保返回时导航栏状态正确
        });

        // 修改：页面加载完成后
        document.addEventListener('DOMContentLoaded', () => {
            fetch('./data/hot_drama/hotdrama.json')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    hotDramaData = data;

                    // 提取所有唯一的 playletTags
                    // ... (这部分不变) ...
                    const allDramasArray = Object.values(hotDramaData);
                    const uniqueTags = new Set();
                    allDramasArray.forEach(drama => {
                        if (drama.playletTags && Array.isArray(drama.playletTags)) {
                            drama.playletTags.forEach(tag => {
                                if (tag && typeof tag === 'string' && tag.trim() !== '') {
                                    uniqueTags.add(tag.trim());
                                }
                            });
                        }
                    });
                    const tagsForNav = ['全部', ...Array.from(uniqueTags).sort()];

                    renderNavigationTabs(tagsForNav); 
                    renderMainPage(); 

                    // 新增：为搜索框和搜索按钮添加事件监听器
                    if (searchInput) {
                        searchInput.addEventListener('input', () => {
                            currentSearchTerm = searchInput.value.trim().toLowerCase();
                            currentPage = 1; // 每次搜索时重置到第一页
                            renderMainPage();
                        });

                        // 可选：如果希望回车键也触发搜索（主要用于表单提交的场景，这里input事件已覆盖）
                        searchInput.addEventListener('keypress', (event) => {
                            if (event.key === 'Enter') {
                                event.preventDefault(); // 防止可能的表单提交行为
                                currentSearchTerm = searchInput.value.trim().toLowerCase();
                                currentPage = 1;
                                renderMainPage();
                            }
                        });
                    } else {
                        console.error("Search input element not found!");
                    }

                    if (searchButton) {
                        searchButton.addEventListener('click', () => {
                            // input事件通常已经处理了，但点击按钮可以作为显式触发
                            currentSearchTerm = searchInput.value.trim().toLowerCase();
                            currentPage = 1;
                            renderMainPage();
                        });
                    } else {
                        console.error("Search button element not found!");
                    }

                })
                .catch(error => {
                    console.error('Error loading hotdrama.json:', error);
                    mainPage.innerHTML = `<p class="text-red-500">加载剧集数据失败：${error.message}</p>`;
                    mainPage.style.display = 'block';
                    detailPage.style.display = 'none';
                    if(dynamicNavTagsList) dynamicNavTagsList.innerHTML = ''; 
                });
        });
    </script>

</body>
</html>
