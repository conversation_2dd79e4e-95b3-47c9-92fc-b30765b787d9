import json
import requests
import traceback
from google import genai
import time 
import sqlite3
from moviepy import VideoFileClip
import io
import os
import tempfile
import logging

STRAPI_URL="http://************:1339"
TOKEN="c378b091baa7b1e9a1864e160718209716113b1229c3ea9fd0c617548ae780a8d3588495f8f935b8bb7e618ec2aa5933550fdbd83a0daf47439733838d3551b9bc0fe06cc94ad60bdc6abedd7e5993e77b6c84dd5604bc4d29e10defef029df7bfad90fac414365afcbf036905e55e3071736a28d7e04c685190ffa03ef0dafc"
sync_text = ""

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')

def insert_entity_data(playletId, materialId, adTitle, videoUrl, summary):
    """Inserts entity data into the database."""
    conn = sqlite3.connect("./data/hotdrama.db")
    cursor = conn.cursor()
    try:
        cursor.execute(f"INSERT INTO drama_summary (playlet_id, material_id, ad_title, video_url, summary) VALUES (?, ?, ?, ?, ?)", 
                       (playletId, materialId, adTitle, videoUrl, summary))
        conn.commit()
        return f"Inserted entity: {playletId}, {materialId}, {adTitle}, {videoUrl}"
    except sqlite3.IntegrityError:
        return f"Entity already exists: {playletId}, {materialId}, {adTitle}, {videoUrl}"
    finally:
        conn.close()

def get_entity_data(materialId):
    """Retrieves the entity path from the database."""
    conn = sqlite3.connect("./data/hotdrama.db")
    cursor = conn.cursor()
    cursor.execute(f"SELECT summary FROM drama_summary WHERE material_id = ?", (materialId,))
    result = cursor.fetchone()
    conn.close()
    if result:
        return result[0]
    return None
        
def append_to_output(new_text):
    """Appends new text to the existing output text."""
    global sync_text

    sync_text = sync_text + new_text + "\n"
    logging.info(new_text) # Add console logging
    sync_arr = sync_text.split("\n")
    sync_text = "\n".join(sync_arr[-50:])  
    return sync_text

def update_episodes(documentId, data):
    msg = ""
    try:
        # Prepare the data for Strapi, ensuring it's wrapped in a 'data' key
    
        strapi_payload = {"data": data}
        header = {
            "Authorization": f"Bearer {TOKEN}",
            "Content-Type": "application/json"
        }
        response = requests.put(f"{STRAPI_URL}/api/episodes/{documentId}", headers=header, json=strapi_payload)
        response.raise_for_status() 
        msg = f"Successfully updated data for {documentId}"
        return response.json(), msg
        
    except Exception as e:
        msg = f"Error update data for {documentId} : {traceback.format_exc()}"
        return None, msg

def find_episodes(data):
    msg = ""
    try:
        header = {
            "Authorization": f"Bearer {TOKEN}",
            "Content-Type": "application/json"
        }
        response = requests.get(f"{STRAPI_URL}/api/episodes?populate=*&filters[playlet_id][$eq]={data['playlet_id']}", headers=header)
        response.raise_for_status() 
        msg = f"Successfully retrieved episodes for playletId {data['playlet_id']}, found {len(response.json()['data'])} records."
        return response.json(), msg
    
    except requests.exceptions.RequestException as e:
        msg = f"Error get data for {data["playlet_id"]} : {traceback.format_exc()}"
        return None, msg
    
def extract_first_five_minutes_to_buffer(input_path):
    msg = ""
    try:
        video_clip = VideoFileClip(input_path)
        duration_to_extract = min(video_clip.duration, 5 * 60)
        subclip = video_clip.subclipped(0, duration_to_extract)

        # 创建一个临时文件
        with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as tmp_file:
            temp_file_path = tmp_file.name

        # 将截取的视频写入临时文件
        subclip.write_videofile(
            temp_file_path,
            codec="libx264",
            audio_codec="aac",
            logger=None
        )

        # 读取临时文件的内容到 BytesIO 对象
        with open(temp_file_path, "rb") as f:
            video_buffer = io.BytesIO(f.read())

        # 删除临时文件
        os.remove(temp_file_path)

        video_clip.close()
        subclip.close()

        video_buffer.seek(0)
        msg = f"Extracted first five minutes of video {input_path} to buffer."
        return video_buffer, msg

    except Exception as e:
        msg = f"Error extracting first five minutes from video {input_path}: {traceback.format_exc()}"
        return None, msg

def realize_video(video_path):

    summary = ""
    msg = ""

    client = genai.Client(api_key="AIzaSyCZf2cxrvhOewsDRjXQ8i_h3gXluIIXIrc")
    video_data, msg = extract_first_five_minutes_to_buffer(video_path)
    if video_data is None:
        return summary, msg 
    
    video_file = client.files.upload(file=video_data, config={"mime_type": "video/mp4"})
    video_file = client.files.get(name=video_file.name)
    while video_file.state != "ACTIVE":
        time.sleep(10)  
        video_file = client.files.get(name=video_file.name)
        
    logging.info(f"{video_file.name} is uploaded and active. start to generate content.") 
    response = client.models.generate_content(
        model="gemini-2.0-flash", contents=[video_file, "请将上述视频故事重写为一篇叙事的小说片段，突出故事的剧情冲突和吸引读者继续读下去的特点。限制在300字之内。"],
    )
    summary = response.text

    novel_sample = ""
    with open("data/novel.json", "r", encoding="utf-8") as f:
        novel = json.load(f)
        for title, item in novel.items():
            novel_sample += f"{title}\n{item["content"]}\n\n"
    logging.info(f"{video_file.name} content generate. start to rewrite content.") 
    response = client.models.generate_content(
        model="gemini-2.0-flash", 
        contents=[f"以下是目前较收欢迎的小说写法:\n{novel_sample}, 以下是一段剧情：{summary}, 从流行小说片段中挑选合适的写法,重写这一段剧情，以达到更好的戏剧化效果。直接输出改写后的内容，不要输出具体的分析细节。"],
    )
    summary = response.text
    msg = f"Successfully summarized video {video_path}"
    return summary, msg

def crawl_from_zhihu():
    tab_types = ["hot", "recommend", "like", "read"]
    channel_types = ["male", "female"]

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
    }

    novel = {}
    for tab_type in tab_types:
        for channel_type in channel_types:
            r_url = f"https://www.zhihu.com/api/vip/km-indep-home-comm/billboard/list?channel_type={channel_type}&filter_key=0&limit=10&offset=0&tab_type={tab_type}"
            
            response = requests.get(r_url, headers=headers)
            response.raise_for_status()
            data = response.json()
            for item in data["data"]:
                if item["title"] not in novel:
                    novel[item["title"]] = {
                        "title": item["title"],
                        "content": item["content_abstract"],
                        "artwork": item["artwork"],
                        "url": item["url"],
                        "labels": item["labels"]
                    }

    with open("data/novel.json", "w", encoding="utf-8") as f:
        json.dump(novel, f, ensure_ascii=False, indent=4)

def summary_test_video(day):
    crawl_from_zhihu()
    global sync_text
    sync_text = ""  # Clear previous output

    data_path = f"./data/{day.replace('-', '/')}/data.json"
    try:
        day_data = json.load(open(data_path, encoding="utf-8"))
    except FileNotFoundError:
        return append_to_output(f"Error: data.json not found for {day}")

    total = len(day_data["content"])
    processed = 0
    for item in day_data["content"]:
        playlet_id = item["playletId"]
        yield append_to_output(f"Processing playletId: {playlet_id}")

        playlet_info_path = f"./data/{day.replace('-', '/')}/{playlet_id}.json"
        try:
            with open(playlet_info_path, encoding="utf-8") as f:
                playlet_info = json.load(f)
        except FileNotFoundError:
            yield append_to_output(f"Error: {playlet_id}.json not found for playletId {playlet_id}")
            break

        #  Extract data and prepare for POST
        try:

            materialList = playlet_info.get("getPlayletInfo", {}).get("materialList", [])
            summarys = []
            for anchor_video in materialList:
                videoUrl = anchor_video.get("videoUrl", "")
                if videoUrl:
                    yield append_to_output(f"Processing videoUrl: {videoUrl}")
                    try:

                        materialId = anchor_video.get("materialId", "")
                        adTitle = anchor_video.get("title", "")
                        summaryData = get_entity_data(materialId)
                        if summaryData:
                            summary = summaryData
                        else:
                            summary, msg = realize_video(videoUrl)
                            yield append_to_output(msg)
                            msg = insert_entity_data(playlet_id, materialId, adTitle, videoUrl, summary)
                            yield append_to_output(msg)
                        summarys.append({"playletId":playlet_id, "videlUrl":videoUrl, "materialId": materialId, "adTitle": adTitle, "summary": summary})
                    except Exception as e:
                        yield append_to_output(f"Error processing videoUrl {videoUrl}: {traceback.format_exc()}")
                        continue

            with open(playlet_info_path.replace(".json", "_summary.json"), "w", encoding="utf-8") as f:
                json.dump(summarys, f, ensure_ascii=False, indent=4)

            data = {
                "playlet_id": str(playlet_id),
                "ai_desc": [s["summary"] for s in summarys]
            }
            
            episodes, msg = find_episodes(data)
            yield append_to_output(msg)

            if episodes and len(episodes["data"]) > 0:
                documentId = episodes["data"][0]["documentId"]
                episodes, msg = update_episodes(documentId, data)
                yield append_to_output(msg)
        
            yield append_to_output(f"Processing data for playletId {playlet_id} has finieshed.")
            processed += 1

        except Exception as e:
            yield append_to_output(f"Error processing data for playletId {playlet_id}, Stack Traceback: {traceback.format_exc()}")

    yield append_to_output(f"process {day} finished. total {total}, processed success {processed}.")        
        
if __name__ == "__main__":
    # Example usage
    playletId = "12345"
    materialId = "67890"
    adTitle = "Sample Title"
    videoUrl = "http://example.com/video.mp4"
    summary = "This is a sample summary."

    # Insert entity data
    msg = insert_entity_data(playletId, materialId, adTitle, videoUrl, summary)
    print(msg)
    # Retrieve entity data
    retrieved_summary = get_entity_data(materialId)
    if retrieved_summary:
        print(f"Retrieved summary: {retrieved_summary}")
    else:
        print("No data found.")
